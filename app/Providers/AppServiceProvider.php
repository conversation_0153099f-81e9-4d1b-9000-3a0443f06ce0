<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Statamic\Statamic;
use Statamic\Facades\GlobalSet;
use Statamic\Facades\Utility;
use Illuminate\Support\Facades\File;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Statamic::vite('app', [
            // 'resources/js/app.js',
            'resources/css/cp.css',
            //'resources/css/app.css',
        ]);
        $this->loadViewsFrom(resource_path('vendor/design-system'), 'design-system');

        Utility::extend(function () {
            Utility::register('design_system')
                ->action(\App\Livewire\DesignSystem::class)
                ->title('Design System')
                ->description('View the design system')
                ->icon('collection')
                ->routes(function ($router) {
                    $router->get('/{type}/{slug1}/{slug2?}/{slug3?}', \App\Livewire\DesignSystem::class);
                });
        });

        $this->registerComponents(resource_path('views/components'));

        view()->composer('*', function ($view) {
            $global = GlobalSet::findByHandle('global')->in('default');
            $view->with('global', $global);
        });
    }

    /**
     * Recursively register components in directories.
     *
     * @param string $directory
     */
    protected function registerComponents($directory, $relativePath = '')
    {
        $directories = File::directories($directory);

        foreach ($directories as $directory) {
            $slug = basename($directory);
            $namespace = trim($relativePath . '.' . $slug, '.');
            Blade::component('components.' . $namespace . '.' . $slug, $namespace);
            $subdirectoryName = basename($directory);
            $this->registerComponents($directory, trim($relativePath . '.' . $subdirectoryName, '.'));
        }

    }
}
