<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\File;
use Symfony\Component\Yaml\Yaml;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Statamic\Facades\Collection;
use Statamic\Facades\Entry;

class DesignSystem extends Component
{
    public $view = 'home';
    public $type = null;
    public $pages = [];
    public $page = null;
    public $components = [];
    public $navGroups = [];
    public $navComponents = [];
    public $pagesNavigationTree = null;
    public $component = [
        'name' => '',
        'status' => 'draft',
        'icon' => '',
        'is_new' => true,
    ];
    public $siteNav = [];
    public $path = null;
    public $slug = null;
    public $group = null;
    public $templates = [];
    public $template = null;
    public $collectionToUse = 'pages';

    public function mount()
    {
        ray()->newScreen()->orange();

        if(config('designsystem.navigation') == ''){
            // use the default 'Pages' tree structure
            $pages_navigation_tree = Yaml::parseFile(base_path('content/trees/collections/pages.yaml'));
            foreach ($pages_navigation_tree['tree'] as $page) {
                $entry = Entry::find($page['entry']);
                // ray($entry)->blue();
                if(!$entry){
                    continue;
                }
                $children = [];
                if (isset($page['children'])) {
                    foreach ($page['children'] as $child) {
                        $childEntry = Entry::find($child['entry']);
                        $children[] = [
                            'id' => $child['entry'],
                            'title' => $childEntry->title,
                            'slug' => $childEntry->slug,
                            'url' => $childEntry->url(),
                            'published' => $childEntry->published(),
                        ];
                    }
                }
                $pages[] = [
                    'id' => $page['entry'],
                    'title' => $entry->title,
                    'slug' => $entry->slug,
                    'url' => $entry->url(),
                    'children' => $children,
                    'published' => $entry->published(),
                ];
            }
            $this->pagesNavigationTree = $pages;
            ray($this->pagesNavigationTree)->blue();
        }

        $this->group = Arr::get(request()->route()->parameters(), 'group');
        $this->type = Arr::get(request()->route()->parameters(), 'type');
        $slug1 = Arr::get(request()->route()->parameters(), 'slug1');
        $slug2 = Arr::get(request()->route()->parameters(), 'slug2');
        $slug3 = Arr::get(request()->route()->parameters(), 'slug3');
        
        $this->components = $this->getComponents(resource_path('views/components'));

        $this->createNav();
        $this->getTemplateList();

        if($this->type == 'page'){
            $this->path = '/'.$slug1;
            if($slug2){
                $this->path .= '/'.$slug2;
            }
            if($slug3){
                $this->path .= '/'.$slug3;
            }
            if($this->path == '/home'){
                $this->path = '';
            }
            // ray($this->path)->orange();
            $this->loadPage($this->path);
        }else{
            $this->slug = $slug1;
            ray($this->slug)->orange();
            $this->loadComponent($this->slug, $this->group);
        }

    }

    protected function getTemplateList()
    {
        $collections = Collection::all();
        
        $templates = [];

        foreach ($collections as $collection) {
            $entries = Entry::query()->where('collection', $collection->handle())->get();

            foreach ($entries as $entry) {
                $template = $entry->template();
                if ($template && !in_array($template, $templates)) {
                    $templates[] = $template;
                }
            }
        }
        // ray($templates)->blue();
        $this->templates = $templates;
    }

    protected function getComponents($directory, $subdirectory = '')
    {
        $components = [];
        $directories = File::directories($directory);

        foreach ($directories as $subdirectoryPath) {
            $configPath = $subdirectoryPath . '/config.yaml';
            if (File::exists($configPath)) {
                $config = Yaml::parseFile($configPath);
                if (isset($config['slug']) && isset($config['name'])) {
                    $components[] = [
                        'slug' => $config['slug'],
                        'name' => $config['name'],
                        'group' => isset($subdirectory) ? str_replace('/', '', $subdirectory) : null,
                    ];
                }
            }
            $subcomponents = $this->getComponents($subdirectoryPath, $subdirectory . '/' . basename($subdirectoryPath));
            $components = array_merge($components, $subcomponents);
        }
        ray($components)->orange();
        return $components;
    }

    protected function createNav()
    {
        $collection = collect($this->components);
        $g = $collection->groupBy('group');
        $grouped = [];
        $ungrouped = [];
        foreach ($g as $key => $value) {
            if($key == null){
                foreach($value as $k => $v){
                    $ungrouped[$v['slug']] = $v;
                }
            }else{
                $grouped[ucfirst($key)] = $value->toArray();
            }
        }
        $this->navGroups = array_keys($grouped);
        $this->navComponents = array_merge($grouped, $ungrouped);
        uksort($this->navComponents, 'strcasecmp');
        // ray($this->navComponents)->orange()->label('Used for nav');
    }

    public function loadPage($path = null)
    {
        $this->component = [
            'name' => '',
            'status' => 'draft',
            'icon' => '',
        ];
        $this->path = $path;
        $this->slug = explode('/', $path);
        $this->slug = end($this->slug);
        if($this->slug == ''){
            $this->path == '/home';
            $this->slug = 'home';
        }
        if($this->slug == 'new-page'){
            $this->viewNewPageConfig();
            return;
        }
        $this->template = $this->slug;
        $collections = Collection::all();
        $this->collectionToUse = 'pages';
        foreach ($collections as $collection) {
            ray($collection->routes()->first())->purple();
            $p = explode('/', $this->path);
            array_pop($p);
            $p = implode('/', $p).'/{slug}';
            ray('Using path: '.$p)->orange();
            // does the path match the route key?
            if($collection->routes()->first() == $p){
                ray($collection->routes()->first().' matches '.$p.', use collection '.$collection->title())->orange();
                $this->collectionToUse = $collection->handle();
                $t = $collection->template();
                $t = explode('/', $t);
                $this->template = end($t);
            }
        }
        // Check for components in page template
        if (File::exists(resource_path('views/pages/'.$this->slug.'/'.$this->slug.'.blade.php'))) {
            // Custom page template
            $file = File::get(resource_path('views/pages/'.$this->slug.'/'.$this->slug.'.blade.php'));
            $components = [];
            preg_match_all('/<x-([a-zA-Z0-9\.-]+)(.*?)>/', $file, $matches);
            ray($matches)->orange();
            $matches = array_filter($matches[1], function($match){
                return $match != 'layouts.app' && $match != 'layout' && $match != 'slot';
            });
            $itemCounts = array_count_values($matches);
            $matches = collect($itemCounts)->map(function ($count, $item) {
                return ['component_slug' => $item, 'count' => $count];
            })->values()->toArray();
            $components = [];
            foreach($matches as $match){
                $path_slug = explode('.', $match['component_slug']);
                $end_slug = end($path_slug);
                if(strpos($match['component_slug'], '.') !== false){
                    $file_path = explode('.', $match['component_slug']);
                    $file_path = implode('/', $file_path);
                }else{
                    $file_path = $end_slug;
                }
                $config = File::get(resource_path('views/components/'.$file_path.'/config.yaml'));
                $components[] = [
                    'component_slug' => $end_slug,
                    'location' => 'blade',
                    'config' => Yaml::parse($config),
                    'count' => $match['count'],
                ];
            }
        }else{
            // Default page template
            // TODO: May be using a different collection to pages so we can't assume the default page template is being used
            $file = File::get(resource_path('views/pages/default/default.blade.php'));
            $components = [];
            preg_match_all('/<x-([a-zA-Z0-9\.-]+)(.*?)>/', $file, $matches);
            ray($matches[1])->blue();
            $matches = array_filter($matches[1], function($match){
                return $match != 'layout' && $match != 'slot';
            });
            $itemCounts = array_count_values($matches);
            $matches = collect($itemCounts)->map(function ($count, $item) {
                return ['component_slug' => $item, 'count' => $count];
            })->values()->toArray();
            $components = [];
            foreach($matches as $match){
                $config = File::get(resource_path('views/components/'.$match['component_slug'].'/config.yaml'));
                $components[] = [
                    'component_slug' => $match['component_slug'],
                    'location' => 'blade',
                    'config' => Yaml::parse($config),
                    'count' => $match['count'],
                ];
            }
        }
        // Check for components in page md file
        if (File::exists(base_path('content/collections/'.$this->collectionToUse.'/'.$this->slug.'.md'))) {
            $fileContent = File::get(base_path('content/collections/'.$this->collectionToUse.'/'.$this->slug.'.md'));
            $pageId = str_replace('id: ', '', explode("\n", $fileContent)[1]);
            if (preg_match('/^---(.*?)---/s', $fileContent, $matches)) {
                $frontMatter = $matches[1];
                foreach ($this->components as $c) {
                    $collection = collect($components);
                    if (strpos($frontMatter, 'type: ' . $c['slug']) !== false) {
                        $exists = $collection->contains(function ($item) use ($c) {
                            return $item['component_slug'] === $c['slug'];
                        });
                        if (!$exists) {
                            ray($match)->orange();
                            $config = File::get(resource_path('views/components/' . $c['slug'] . '/config.yaml'));
                            $components[] = [
                                'component_slug' => $c['slug'],
                                'location' => 'md',
                                'config' => Yaml::parse($config),
                                'count' => 1,
                            ];
                        } else {
                            foreach ($components as &$cc) {
                                if ($cc['component_slug'] === $c['slug']) {
                                    $cc['count']++;
                                    if($cc['location']=='blade'){
                                        $cc['location'] = 'blade & md';
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
        $this->page = [
            'id' => $pageId ?? null,
            'name' => $this->slug,
            'components_used' => $components,
        ];
        ray($components)->purple();
        $this->view = 'view-page';
        $this->dispatch('page-loaded', ['url' => $path]);
    }

    public function loadComponent($slug = null, $group = null)
    {
        $this->slug = $slug;
        if (!$this->slug) {
            $this->view = 'home';
            $this->component = [
                'name' => '',
                'status' => 'draft',
                'icon' => '',
            ];
            $this->page = [
                'name' => '',
            ];
            $this->dispatch('component-loaded', ['slug' => '']);
            return;
        }
        if($slug == 'new-component'){
            $this->viewNewComponentConfig();
            return;
        }
        if($group){
            $this->path = $group.'/'.$slug;
        }else{
            $this->path = $slug;
        }
        $config = File::get(resource_path("views/components/{$this->path}/config.yaml"));
        $this->component = Yaml::parse($config);
        $this->component['group'] = $group;
        $this->component['is_new'] = false;
        $use_in = [];
        // scan the blade files in the $this->templates for any occurance of '<x-feature', If found, add the page to the use_in array
        foreach ($this->templates as $template) {
            // check if the file exists
            if (!File::exists(resource_path('views/'.$template.'.blade.php'))) {
                // ray(resource_path('views/'.$template.'.blade.php').' does not exist')->red();
                continue;
            }
            $content = File::get(resource_path('views/'.$template.'.blade.php'));
            if (Str::contains($content, '<x-'.$slug)) {
                $use_in[] = [
                    'filename' => $template,
                    'type' => 'template',
                ];
            }
        }
        $collections = Collection::all();
        foreach ($collections as $collection) {
            if ($collection->handle() != 'pages') {
                continue;
            }
            $entries = Entry::query()->where('collection', $collection->handle())->get();
            foreach ($entries as $entry) {
                $filePath = $entry->path();
                $fileContent = File::get($filePath);
                // Extract the front matter
                if (preg_match('/^---(.*?)---/s', $fileContent, $matches)) {
                    $frontMatter = $matches[1];
                    if(strpos($frontMatter, 'type: '.$slug) !== false){
                        $use_in[] = [
                            'filename' => $entry->title,
                            'template' => $entry->template,
                            'type' => 'entry',
                            'entry_id' => $entry->id(),
                            'edit_path' => cp_route('collections.entries.edit', ['collection' => $collection->handle(), 'entry' => $entry->id()]),
                        ];
                    }
                }
            }
        }
        
        $this->component['used_in'] = $use_in;
        // ray($use_in)->blue();
        $this->view = 'view-component';
        $this->dispatch('component-loaded', ['path' => $this->path]);
    }

    public function loadView($view)
    {
        $this->view = $view;
    }

    public function viewNewPageConfig()
    {
        $this->page = [
            'name' => '',
            'status' => 'draft',
            'is_new' => true,
        ];
        $this->view = 'page-config';
        $this->dispatch('page-loaded', ['url' => '/new-page']);
    }

    public function pageConfig()
    {
        $validated = $this->validate([ 
            'page.name' => 'required|min:3',
            'page.status' => 'required',
        ]);
        if($this->page['is_new']) {
            ray('create')->orange();
            $this->createPage($validated);
        } else {
            ray('update')->orange();
            $this->updatePage($validated);
        }
    }

    public function createPage($validated)
    {
        ray($validated)->green();
        $slug = strtolower(str_replace(' ', '-', $validated['page']['name']));
        ray('createPage validated - '.$slug)->green();
        $path = $slug;
        ray('createPage validated - '.$path)->green();
        File::copyDirectory(resource_path('views/vendor/design-system/page-stub'), resource_path('views/pages/'.$path));
        $config = [
            'slug' => Str::slug($validated['page']['name']),
            'name' => ucfirst($validated['page']['name']),
            'status' => $validated['page']['status'],
        ];
        File::put(resource_path('views/pages/'.$path.'/config.yaml'), Yaml::dump($config));
        $files = File::allFiles(resource_path('views/pages/'.$path));
        foreach ($files as $file) {
            $newFilename = str_replace('stub', $slug, $file->getFilename());
            $newPath = $file->getPath().'/'.$newFilename;
            File::move($file->getPathname(), $newPath);
            // ray($newPath)->orange();
            $content = File::get($newPath);
            $content = str_replace('StubPage', ucfirst(Str::camel($slug)).'Page', $content);
            $content = str_replace('stub', $slug, $content);
            File::put($newPath, $content);
        }
        // Create the page in Statamic (md)
        $page = [
            'title' => $validated['page']['name'],
            'slug' => $slug,
            'template' => 'pages/'.$slug.'/'.$slug,
        ];
        $entry = Entry::make()
            ->collection('pages')
            ->data($page)
            ->slug($slug)
            ->published(true);
        $entry->save();
        $this->dispatch('page-loaded', ['path' => $path]);
    }

    public function viewNewComponentConfig()
    {
        $this->component = [
            'name' => '',
            'status' => 'draft',
            'group' => '',
            'is_new' => true,
            'icon' => 'fa-duotone fa-cube',
        ];
        $this->view = 'component-config';
        $this->dispatch('component-loaded', ['path' => 'new-component']);
    }

    public function componentConfig()
    {
        $validated = $this->validate([ 
            'component.name' => 'required|min:3',
            'component.group' => '',
            'component.status' => 'required',
            'component.icon' => 'required',
        ]);
        if($this->component['is_new']) {
            ray('create')->orange();
            $this->createComponent($validated);
        } else {
            ray('update')->orange();
            $this->updateComponent($validated);
        }
    }

    public function createComponent($validated)
    {
        ray($validated)->green();
        $slug = strtolower(str_replace(' ', '-', $validated['component']['name']));
        ray('createComponent validated - '.$slug)->green();
        if(isset($validated['component']['group']) && $validated['component']['group']!=''){
            $path = strtolower($validated['component']['group']).'/'.$slug;
        }else{
            $path = $slug;
        }
        ray('createComponent validated - '.$path)->green();
        File::copyDirectory(resource_path('views/vendor/design-system/component-stub'), resource_path('views/components/'.$path));
        $config = [
            'slug' => Str::slug($validated['component']['name']),
            'name' => ucfirst($validated['component']['name']),
            'status' => $validated['component']['status'],
            'icon' => $validated['component']['icon'],
        ];
        File::put(resource_path('views/components/'.$path.'/config.yaml'), Yaml::dump($config));
        $files = File::allFiles(resource_path('views/components/'.$path));
        foreach ($files as $file) {
            $newFilename = str_replace('stub', $slug, $file->getFilename());
            $newPath = $file->getPath().'/'.$newFilename;
            File::move($file->getPathname(), $newPath);
            // ray($newPath)->orange();
            $content = File::get($newPath);
            if($newFilename == $slug."--preview.blade.php"){
                $content = str_replace('stub', str_replace('/', '.', $path), $content);
            }else{
                $content = str_replace('stub', $slug, $content);
                $content = str_replace('x-data="'.$slug.'"', 'x-data="'.ucfirst($slug).'"', $content);
            }
            File::put($newPath, $content);
        }
        $this->dispatch('component-loaded', ['path' => $path]);
    }

    public function updateComponent($validated)
    {
        $slug = strtolower(str_replace(' ', '-', $validated['component']['name']));
        $config = [
            'slug' => Str::slug($validated['component']['name']),
            'name' => ucfirst($validated['component']['name']),
            'status' => $validated['component']['status'],
            'icon' => $validated['component']['icon'],
        ];
        if(isset($this->component['parent']) && $this->component['parent']!=''){
            $path = resource_path('views/components/'.strtolower($this->component['parent']).'/'.$slug.'/config.yaml');
        }else{
            $path = resource_path('views/components/'.$slug.'/config.yaml');
        }
        ray($path)->purple();
        File::put($path, Yaml::dump($config));
        $this->loadComponent($slug);
    }

    public function editComponent($slug = null)
    {
        // load component config
        $this->loadComponent($slug);
        $this->view = 'component-config';
        // $this->loadComponent($slug);
    }

    public function deleteComponent($slug, $group = null)
    {
        if($group){
            $path = $group.'/'.$slug;
        }else{
            $path = $slug;
        }
        File::deleteDirectory(resource_path('views/components/'.$path));
        $this->view = 'home';
        $this->component = [
            'name' => '',
            'group' => '',
            'status' => 'draft',
            'icon' => '',
            'is_new' => true,
        ];
        $this->dispatch('component-loaded', ['slug' => null]);
        $this->mount();
    }

    public function render()
    {
        return view('design-system::ds-base')
        ->layout('design-system::layouts.ds-layout');
    }
}
