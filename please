#!/usr/bin/env php
<?php

use Symfony\Component\Console\Input\ArgvInput;

define('LARAVEL_START', microtime(true));

// Register the Composer autoloader...
require __DIR__.'/vendor/autoload.php';

// Bootstrap Laravel...
$app = require_once __DIR__.'/bootstrap/app.php';

// Rebind the kernel...
Statamic\Console\Please\Application::rebindKernel();

// Handle the command...
$status = $app->handleCommand(new ArgvInput);

exit($status);
