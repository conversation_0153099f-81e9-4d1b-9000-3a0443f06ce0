.ds-news {
    .news-listings-grid {
        @apply grid grid-cols-2 gap-x-6 md:gap-x-8 lg:gap-x-12 gap-y-8 md:gap-y-4 lg:gap-y-6 divide-y;
        
        li {
            @apply flex md:block md:pt-4 lg:pt-6 md:first:pt-0;
        }

        // Apply padding and border only to every 2nd <li> (starting from 2nd item)
        li:nth-child(2) {
            @apply md:pt-0 md:!border-t-0;
        }
    }
}