.ds-home {
   .about-image-carousel {
        .carousel-cell {
            @apply mx-0;
            .ds-cards--image {
                @apply aspect-3/4 h-full w-full;
            }
        }
        .custom-nav {
            @apply absolute bottom-0 inset-x-0 flex items-end justify-end space-x-0.5;
            .button {
                @apply aspect-square w-16 h-16 bg-black bg-opacity-30 text-4xl;
                &--next {
                    @apply hover:bg-opacity-100 hover:bg-sage-500 duration-200;
                }
                &--previous {
                    @apply hover:bg-opacity-100 hover:bg-sage-500 duration-200;
                }
            }
        }
    }
    .food, .drinks {
        .ds-fifty-fifty {
            &__right-block {
                @apply px-8 py-20 md:p-12 lg:p-24;
            }
            .ds-image {
                @apply object-cover w-full md:h-full h-80;
                img {
                    @apply object-cover w-full md:h-full;
                }
            }
        }
    }
    .gallery-carousel {
        .carousel-cell {
            @apply mx-0;
        }
        .ds-cards--image {
            @apply aspect-4/3 md:aspect-video;
        }
        .custom-nav {
            @apply h-16 inset-y-0 top-1/2 -translate-y-1/2 absolute space-x-0;
            .button {
                @apply absolute aspect-square w-16 h-16 bg-black hover:bg-sage-500 bg-opacity-30 hover:bg-opacity-100 duration-200 text-4xl;
                &--next {
                    @apply left-[calc(100vw-4rem)] md:left-[90vw] lg:left-[93vw] z-10;
                }
                &--previous {
                    @apply left-0 md:left-[calc(10vw-4rem)] lg:left-[calc(7vw-4rem)];
                }
            }
        }
    }
    .suppliers-carousel {
        @apply mx-auto w-screen relative;
        .carousel-cell {
            @apply aspect-3/4 mx-1 w-[calc(100vw/1.25)] md:w-[calc(100vw/3.5)] lg:w-[calc(100vw/4.5)] max-w-[350px];
            .ds-cards--image {
                @apply aspect-3/4 h-full w-full;
            }
        }
        // .flickity-enabled {
        //     @apply md:-mx-[calc(100vw-175px)];
        // }
        .custom-nav {
            @apply hidden md:block h-16 inset-y-0 top-1/2 -translate-y-1/2 absolute space-x-0;
            .button {
                @apply absolute aspect-square w-16 h-16 bg-black hover:bg-sage-500 bg-opacity-30 hover:bg-opacity-100 duration-200 text-4xl;
                &--next {
                    @apply left-[90vw] lg:left-[93vw] z-10;
                }
                &--previous {
                    @apply left-[calc(10vw-4rem)] lg:left-[calc(7vw-4rem)];
                }
            }
        }
        .flickity-page-dots {
            @apply -bottom-12;
        }
    }
    .reviews {
        .ds-section-header {
            &__title { 
                line-height: 32px !important;
                @apply text-2xl lg:text-3xl lg:!leading-tight;
               
            }
            &__eyebrow {
                @apply mb-4;
            }
        }
        .ds-fifty-fifty {
            &__content-container {
                @apply px-8 py-20 md:p-12 lg:p-24;
            }
            &__right-block {
                @apply relative bg-sage-500;
            }
        }
    }
}