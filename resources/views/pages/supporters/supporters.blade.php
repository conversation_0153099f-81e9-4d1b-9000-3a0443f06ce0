<x-layout :context="$__data">
    <div x-data="SupportersPage" class="ds-supporters">
         <section class="ds-section-padding">
            <div class="ds-site-padding">
                <x-section-header title="Our supporters" class="text-center">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.
                </x-section-header>
                <ul role="list" class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-6 lg:gap-y-8 mt-10 lg:mt-12">
                    @php
                        $data = [
                            'team' => [
                                ["image" => "/img/supporters/placeholder-keir-starmer.jpg", "name" => "Keir Starmer", "position" => "Prime Minister (UK)", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-mary-creagh.jpg", "name" => "<PERSON>", "position" => "Vice-Chair, All-Party Parliamentary Group for Food and Health (Liberal Democrat)", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-patrick-vallance.jpg", "name" => "Sir Patrick Vallence", "position" => "Minister for Science", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-keir-starmer.jpg", "name" => "Keir Starmer", "position" => "Prime Minister (UK)", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-mary-creagh.jpg", "name" => "Mary Creag", "position" => "CBE MP", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-patrick-vallance.jpg", "name" => "Sir Patrick Vallence", "position" => "Minister for Science", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-keir-starmer.jpg", "name" => "Keir Starmer", "position" => "Prime Minister (UK)", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-mary-creagh.jpg", "name" => "Mary Creag", "position" => "CBE MP", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-patrick-vallance.jpg", "name" => "Sir Patrick Vallence", "position" => "Minister for Science", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-keir-starmer.jpg", "name" => "Keir Starmer", "position" => "Prime Minister (UK)", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-mary-creagh.jpg", "name" => "Mary Creag", "position" => "CBE MP", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                                ["image" => "/img/supporters/placeholder-patrick-vallance.jpg", "name" => "Sir Patrick Vallence", "position" => "Minister for Science", "flag" => "https://www.worldometers.info/img/flags/uk-flag.gif"],
                            ]
                        ];
                    @endphp
                    @foreach($data['team'] as $slide)
                        <x-card.team-member :slide="$slide" />
                    @endforeach
                </ul>
            </div>
        </section>
    </div>
</x-layouts.app>