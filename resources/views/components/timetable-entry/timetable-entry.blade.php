@props([
    'slide' => [],
    'icon' => 'fa-solid fa-user',
    'title' => null,
    'times' => null,
    'instructor' => null,
])
@php
    if (!empty($slide)) {
        $icon = $slide['icon'] ?? 'fa-solid fa-user';
        $title = $slide['title'] ?? '';
        $times = $slide['times'] ?? '';
        $instructor = $slide['instructor'] ?? '';
    }
@endphp

<div x-data="Timetable-entry" data-module="ds-timetable-entry" {{ $attributes->merge(['class' => 'ds-timetable-entry']) }}>
    <h4>{{ $title  ?? '' }}</h4>
    <p>{{ $times ?? '' }}</p>
    <p class="ds-timetable-entry__instructor-details">
        <span>
            <i class="{{ $icon }}"></i>
        </span>
        {{ $instructor ?? '' }}
    </p>
</div>