@props([
    'crumbs' => []
])

<nav x-data="Crumb" data-module="ds-breadcrumb" {{ $attributes->merge(['class' => 'ds-breadcrumb']) }} aria-label="Breadcrumb">
    <ol role="list">
        @foreach ($crumbs as $crumb) 
        <li>
            <a href="{{ $crumb['url'] ?? '#' }}">
                {{ $crumb['label'] }}
            </a>
            @if(!$loop->last)
                <svg fill="currentColor" viewBox="0 0 20 20" aria-hidden="true" style="max-height:15px">
                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                </svg>
            @endif
        </li>
        @endforeach
    </ol>
</nav>