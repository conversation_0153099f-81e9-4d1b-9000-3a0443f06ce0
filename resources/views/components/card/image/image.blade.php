@props([
    'item' => [],
    'glideWidth' => null,
    'glideHeight' => null
])

<div x-data="Image" data-module="ds-cards--image" {{ $attributes->merge(['class' => 'ds-cards--image']) }}>
    @if($glideWidth)
    @foreach (Statamic::tag('glide:generate')->src($item)->width($glideWidth)->height($glideHeight) as $image)
    <img src="{{ $image['url'] }}" alt="{{ $item['alt'] ?? '' }}" class="object-cover w-full h-full">
    @endforeach
    @else
    <img src="{{ $item['image'] ?? '' }}" alt="{{ $item['alt'] ?? '' }}" class="object-cover w-full h-full">
    @endif
</div>