@props([
    'item' => [],
    'testimonial' => '',
    'name' => '',
    'position' => '',
    'location' => '',
    'source' => '/img/google-g-logo.svg',
    'alt' => 'Google G logo',
])

@php
    $testimonial = $item['testimonial'] ?? $testimonial;
    $name = $item['name'] ?? $name;
    $position = $item['position'] ?? $position;
    $location = $item['location'] ?? $location;
    $source = $item['source'] ?? $source;
    $alt = $item['alt'] ?? $alt;
@endphp

<div x-data="Testimonial" data-module="ds-card--testimonial" {{ $attributes->merge(['class' => 'ds-card--testimonial']) }}>
    <figure class="ds-card--testimonial__container">
        @isset($item['rating'])
            <div class="ds-card--testimonial__rating">
                <div class="ds-card--testimonial__stars">
                    @for ($i = 0; $i < $item['rating']; $i++)
                        <i class="fa-solid fa-star"></i>
                    @endfor
                </div>
                @if (!empty($source))
                    <img src="{{ $source }}" alt="{{ $alt }}">
                @endif
            </div>
        @endisset
        <blockquote>{!! $testimonial !!}</blockquote>
        <figcaption class="ds-card--testimonial__name">{{ $name }}</figcaption>
        <p class="ds-card--testimonial__position">{{ $position }}</p>
    </figure>
</div>
