@props([
	'image' => null,
	'url' => '#',
	'badge' => null,
	'carousel' => null,
    'title' => null,
	'description' => null,
	'modal',
   'item' => '',
])

<a x-data="CardDefault" href="{{ $item['url'] }}" data-module="ds-card--default" {{ $attributes->merge(['class' => 'ds-card--default group']) }}>
    @if($badge)
        <x-badge>{{ $badge }}</x-badge>
    @endif
    @if($carousel)
        <div class="ds-card--default__carousel">
            {{ $carousel }}
        </div>
    @endif
    @isset($image)
        <div class="ds-card--default__image-container group">
            <a data-fancybox data-src="#{{ $modal }}" class="absolute h-full w-full">
                @isset($icon)
                    <div class="ds-card--default__overlay">
                    <div class="ds-card--default__icon-container">
                        <i class="{{ $icon }}"></i>
                    </div>
                    </div>
                @endisset
                <div class="ds-card--default__image">
                    <img class="" src="{{ $item['image'] ?? '' }}" alt="{{ $item['alt'] ?? '' }}">
                </div>
            </a>
        </div>
    @endisset
    <div class="ds-card--default__image-container">
        <div class="ds-card--default__overlay"></div>
        <img class="ds-card--default__image" src="{{ $item['image'] ?? '' }}" alt="{{ $item['title'] ?? '' }}">
    </div>
    @if($item['title'])
    <div class="ds-card--default__content">
        <div>
            <div class="ds-card--default__title">
                {{ $item['title'] }}
            </div>
            <div class="ds-card--default__description">
                {{ $item['description'] }}
            </div>
 
        </div>
        <x-button type="text-icon" icon_append="fa-regular fa-arrow-right" class="mt-6 md:mt-8">More details</x-button>
    </div>
    @endif
</a>