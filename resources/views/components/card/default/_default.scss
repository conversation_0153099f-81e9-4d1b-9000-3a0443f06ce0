.ds-card--default {
    @apply bg-white relative rounded-lg shadow-md overflow-hidden flex flex-col group-hover:shadow-[0_40px_40px_-30px_rgba(29,22,4,0.25)] duration-200 hover:transform hover:-translate-y-1 hover:-translate-x-0 hover:scale-[1.01];
    &__image-container {
        @apply relative aspect-3/2 cursor-pointer overflow-hidden;
    }
    &__image {
        @apply relative aspect-3/2 object-cover h-full w-full scale-100 group-hover:scale-105 duration-200;
    }
    &__overlay {
        @apply absolute z-10 inset-0 flex items-center justify-center bg-teal-650/10;
    }
    &__icon-container {
        @apply text-white text-4xl lg:text-[44px] opacity-0 group-hover:opacity-100 -translate-y-4 group-hover:translate-y-0 duration-300 flex items-center justify-center z-20 ;
    }
    &__content {
        @apply flex flex-1 flex-col justify-between items-start px-6 pt-5 pb-6;
    }
    &__title {
        @apply text-2xl font-semibold text-blue-550 mb-3;
    }
    &__description {
        @apply text-sm lg:text-base;
    }
    &__carousel {
        @apply aspect-3/2;
    }
    .ds-list {
        @apply text-sm space-y-0.5;
        &.ds-list--icons {
            li {
                @apply space-x-2;
                i {
                    @apply mt-0.5;
                }
            }
        }
    }
}