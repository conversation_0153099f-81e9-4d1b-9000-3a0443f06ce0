@props([
    'slide' => [],
    'image' => null,
    'icon' => 'fa-light fa-circle-plus',
    'name' => '',
    'position' => '',
    'modal',
])
@php
    if (!empty($slide)) {
        $image = $slide['image'];
        $icon = $slide['icon'] ?? 'fa-light fa-circle-plus';
        $name = $slide['name'] ?? '';
        $position = $slide['position'] ?? '';
         $modal = $slide['modal'];
    }
@endphp

<li x-data="Team-member" data-module="ds-team-member" {{ $attributes->merge(['class' => 'ds-team-member group']) }}>
    <a data-fancybox data-src="#{{ $modal }}" class="absolute h-full w-full">
        <div class="absolute inset-0 bg-sage-800 bg-opacity-25 opacity-100 group-hover:opacity-0 transition-opacity duration-300 z-10"></div>
        <p class="ds-team-member__position">{!! $position ?? '' !!}</p>
        <div class="relative overflow-hidden z-0">
            <img src="{{ $image }}" alt="{{ $name }}" class="ds-team-member__image">
        </div>
        <div class="ds-team-member__overlay">
            <h3 class="ds-team-member__name">{{ $name }}</h3>
            <p class="font-sans tracking-wider text-sm md:translate-y-24 text-yellow-150 opacity-0 group-hover:md:translate-y-20 group-hover:opacity-100 transition-all -mt-4 group-hover:mt-0 duration-200">VIEW PROFILE</p>
        </div>
    </a>
</li>