@props([
    'item' => [],
])

<div x-data="Project" data-module="ds-cards--project" {{ $attributes->merge(['class' => 'ds-cards--project group']) }}>
    <a href="{{ $item['link'] instanceof \Statamic\Entries\Entry ? $item['link']->url() : $item['link'] }}" target="_blank" class="w-full h-full">
        <div class="relative overflow-hidden aspect-3/4">
            <div class="ds-cards--project__bg-gradient"></div>
            <img src="{{ $item['image'] ?? '' }}" alt="{{ $item['title'] ?? '' }}" width="800" class="ds-cards--project__image" />
        </div>
        <div class="ds-cards--project__content">
            <div class="ds-cards--project__content-inner">
                <div class="ds-cards--project__description">
                    {{ $item['title'] }}
                </div>
                <div class="ds-cards--project__details">
                    <i class="fa-regular fa-calendars"></i> {{ $item['date'] }}
                </div>
            </div>
        </div>
    </a>
</div>
