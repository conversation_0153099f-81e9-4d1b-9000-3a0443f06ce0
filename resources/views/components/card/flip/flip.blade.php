@props([
    'item' => [],
])

<a href="{{ $item['link'] ?? '' }}" target="{{ $item['open_in_new_window'] ?? '' }}" data-module="ds-cards--flip" {{ $attributes->merge(['class' => 'ds-cards--flip']) }}>
    <div class="ds-cards--flip__container">
        <!-- Content for front face -->
        <div class="ds-cards--flip__front">
            <div class="ds-cards--flip__front--content">
                <div class="ds-cards--flip__front--content--image">
                    <img src="{{ $item['image'] ?? '' }}" alt="{{ $item['title'] ?? '' }}" class="object-cover w-full h-full rounded-full">
                </div>
                <div class="relative flex">
                    <div class="mr-12">
                        <h3 class="ds-cards--flip__title">{{ $item['title'] ?? '' }}</h3>
                    </div>
                    <div class="ds-cards--flip__arrow">
                        <i class="fa-sharp fa-regular fa-arrow-right-long"></i>
                    </div>
                </div>
            </div>
        </div>
        <!-- Content for back face -->
        <div class="ds-cards--flip__back">
            <div class="relative flex">
                <div class="mr-12">
                    <h3 class="ds-cards--flip__title">{{ $item['title'] ?? '' }}</h3>
                </div>
                 <div class="ds-cards--flip__arrow">
                    <i class="fa-sharp fa-regular fa-arrow-right-long"></i>
                </div>
            </div>
            <div class="ds-cards--flip__back--content">
                <p>{!! $item['content'] ?? '' !!}</p>
            </div>
        </div>
    </div>
</a>