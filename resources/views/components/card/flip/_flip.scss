.ds-cards--flip {
    @apply text-left aspect-4/3 bg-transparent cursor-pointer w-full h-full perspective-1000;
    &__container {
        @apply relative transform-style-3d transition-transform w-full h-full duration-700 rounded-xl hover:rotate-y-180;
        -webkit-transform-style: preserve-3d;
    }
    &__eyebrow {
        @apply block text-xs mb-2.5;
    }
    &__title {
        @apply font-medium text-3xl md:text-4xl -mb-2 text-white;
    }
    &__arrow {
        @apply absolute right-0 bottom-0 text-xl;
    }
    &__front {
        @apply absolute backface-hidden w-full h-full bg-gradient-to-tr from-amber-850 from-40% to-amber-850 to-90% text-white rounded-xl p-6 md:p-8 flex flex-col duration-200;
        &--content {
            @apply relative flex flex-col flex-grow justify-between;
            &--image {
                @apply rounded-full w-16 h-16 sm:w-24 sm:h-24 bg-gray-200 ring-[6px] md:ring-[12px] ring-stone-100/20 ml-auto;
            }
        }
    }
    &__back {
        @apply absolute backface-hidden w-full h-full bg-stone-50 text-white overflow-hidden rounded-xl p-6 md:p-8 flex flex-col;
        transform: rotateY(180deg);
        &--content {
            @apply relative flex flex-col flex-grow justify-end text-neutral-600 lg:text-lg;
        }
        .ds-cards--flip {
            &__title {
                @apply text-amber-1000;
            }
            &__arrow {
                @apply text-[#032124] top-2;
            }
        }
    }
}