.ds-card-horizontal {
    @apply flex flex-col md:flex-row relative border bg-white border-gray-200 shadow px-4 py-6 md:pl-8 md:pr-12 list-none gap-x-8 gap-y-3 hover:shadow-lg duration-200;
    &__image {
        @apply max-w-44 md:max-w-52 object-contain;
    }
    &__title {
        @apply font-black text-gray-950 text-xl leading-tight md:text-2xl mb-1.5;
    }
    &__description {
        @apply text-sm md:text-base text-gray-450;
    }
    &__icon-container {
        @apply absolute top-4 right-4 w-[18px] h-[22px] overflow-hidden;
    }
    &__icon-first {
        @apply absolute text-red-650 text-2xl group-hover:translate-x-full group-hover:-translate-y-full duration-300 ease-in-out group-hover:opacity-0;
    }
    &__icon-second {
        @apply absolute text-gray-950 text-2xl -translate-x-full translate-y-full group-hover:-translate-x-0 group-hover:-translate-y-0 duration-300 ease-in-out opacity-0 group-hover:opacity-100
    }
}