.ds-cards--news {
    @apply relative duration-200 cursor-pointer flex flex-row;
    &__image-container {
        @apply relative overflow-hidden aspect-video;
    }
    &__image {
        @apply aspect-video object-cover duration-500 ease-in-out transition group-hover:scale-110;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        -webkit-transform-style: preserve-3d;
        transform-style: preserve-3d;
    }
    &__content {
        @apply flex flex-col justify-between flex-1 relative w-full text-gray-950 group-hover:text-red-650 duration-200;
    }
    &__title {
        @apply text-lg leading-tight lg:text-xl lg:leading-tight font-black mb-2;
    }
    &__description {
        @apply flex-1 text-gray-450 text-sm lg:text-base mb-4 duration-500;
    }
    &__badge {
        @apply relative;
    }
    &__datestamp-container {
        @apply mt-auto;
    }
    &__datestamp {
        @apply text-xs text-gray-450;
    }
    &.ds-cards--news--primary {
        @apply flex-col md:flex-row items-stretch gap-y-3 md:gap-y-0 md:gap-x-3 lg:gap-x-4;
        .ds-cards--news {
            &__image-container {
                @apply md:max-w-[42%] flex-shrink-0;
            }
        }
    }
    &.ds-cards--news--feature {
        @apply flex-col gap-y-4 lg:gap-y-6;
        .ds-cards--news {
            &__title {
                @apply leading-tight md:leading-tight text-xl md:text-2xl lg:text-3xl uppercase;
            }
        }
    }
}