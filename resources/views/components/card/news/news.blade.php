@props([
	'item' => [],
    'type' => 'primary',
])

<a href="{{ $item['href'] ?? '' }}" x-data="News" data-module="ds-cards--news" {{ $attributes->merge(['class' => 'ds-cards--news group ds-cards--news--' .$type]) }}>
    <div class="ds-cards--news__image-container">
        <img src="{{ $item['image'] ?? '' }}" alt="{{ $item['title'] ?? '' }}" width="800" class="ds-cards--news__image" />
    </div>
    <div class="ds-cards--news__content">
        <div class="ds-cards--news__title">
            {!! $item['title'] !!}
        </div>
        @if($type === 'feature' && isset($item['description']))
            <div class="ds-cards--news__description">
                {!! $item['description'] !!}
            </div>
        @endif
        @if(isset($item['date']))
            <div class="ds-cards--news__datestamp-container">
                <time datetime="{{ $item['date']}}" class="ds-cards--news__datestamp">
                    {{ $item['date'] }}
                </time>
            </div>
        @endif
    </div>
</a>
