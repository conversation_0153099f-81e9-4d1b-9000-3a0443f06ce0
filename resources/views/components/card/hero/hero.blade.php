@props(['item'])

<div x-data="CardHero" data-module="ds-card--hero" class="ds-card--hero">
    <div class="ds-card--hero__overlay-gradient"></div>
    <img class="ds-card--hero__image" src="{{ $item['image'] }}" alt="{{ $item['title'] }}">
    <div class="ds-card--hero__caption" data-aos="fade-up" data-aos-duration="1000">
        <div class="ds-card--hero__caption--inner light-mode">
            @isset($item['title'])
                <h1 class="ds-card--hero__title !leading-tight">{{ $item['title'] }}</h1>
            @endisset
            @isset($item['content'])
                <div class="ds-card--hero__text">
                    {!! $item['content'] !!}
                </div>
            @endisset
            @if(isset($item['btn_url']) && $item['btn_url'] != '')
                <x-button type="primary" href="{{ $item['btn_url'] instanceof \Statamic\Entries\Entry ? $item['btn_url']->url() : $item['btn_url'] }}" class="mt-8">
                    {{ $item['btn_label'] ?? 'Find out more' }}
                </x-button>
            @endif
        </div>
    </div>
</div>
