<section x-data="OurValues" data-module="ds-our-values" {{ $attributes->merge(['class' => 'ds-our-values']) }}>
   <div class="ds-site-padding max-w-screen-2xl">
       <div class="grid gap-6 md:grid-cols-12 lg:gap-12">
           <div class="md:col-span-5 lg:col-span-4 text" data-aos="fade-up" data-aos-duration="1000">
               <x-section-header eyebrow="Our values" :title="$reusable_components->strapline" />
               <div class="text-block">
                   {!! $reusable_components->content !!}
               </div>
               <x-button type="primary" href="/our-approach" icon_append="fa-solid fa-angle-right" class="ds-button--primary__icon mt-4 !hidden md:!inline-flex">
                   Our approach
               </x-button>
           </div>
           <div class="md:col-span-6 lg:col-span-8 md:col-start-7 lg:col-start-6">
               <div class="grid gap-10 lg:grid-cols-2 lg:gap-12">
                   @foreach($reusable_components->the_values as $value)
                   <x-feature :icon="$value->icon" :title="$value->title" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="200">
                       We act with honesty & transparency and put our client's business interests at the forefront of what we do.
                   </x-feature>
                   @endforeach
               </div>
           </div>
       </div>
   </div>
</section>