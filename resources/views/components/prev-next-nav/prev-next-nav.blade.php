@props([
    'entryId' => null,
    'entries' => null,
    'icon' => 'fa-solid fa-grid',
    'back' => null,
])

<nav x-data="PrevNextNav" data-module="ds-prev-next-nav" {{ $attributes->merge(['class' => 'ds-prev-next-nav']) }}>

    
    @php
    $entry = Statamic\Facades\Entry::find($entryId);
    if($entries === null){
        $collectionHandle = $entry->collection()->handle();
        $entries = Statamic\Facades\Entry::query()
            ->where('collection', $collectionHandle)
            ->orderBy('date', 'asc')
            ->get();
    }
    $currentIndex = $entries->search(function ($item) use ($entry) {
        return $item->id() === $entry->id();
    });
    $nextEntry = $entries->get($currentIndex + 1);
    $previousEntry = $entries->get($currentIndex - 1);
    @endphp

    @if($previousEntry)
    <a href="{{ $previousEntry->url ?? '/' }}" class="ds-prev-next-nav__prev group">
        <div class="ds-prev-next-nav__prev--arrow">
            <i class="fa-sharp fa-light fa-arrow-left-long"></i>
        </div>
        <div class="ds-prev-next-nav__prev--content">
            <p class="text-sm text-green-750">Prev</p>
            <p class="duration-200 lg:text-lg group-hover:text-amber-1000">{{ $previousEntry->title ?? '' }}</p>
        </div>
    </a>
    @else
    <div class="ds-prev-next-nav__prev group">&nbsp;</div>
    @endif

    <a href="{{ $back ?? '' }}" class="ds-prev-next-nav__back">
        <span class="text-xl leading-none">
            <i class="{{ $icon ?? '' }}"></i>
        </span>
    </a>

    @if($nextEntry)
        <a href="{{ $nextEntry->url ?? '/' }}" class="ds-prev-next-nav__next group">
            <div class="ds-prev-next-nav__next--content">
                <p class="text-sm text-green-750">Next</p>
                <p class="duration-200 lg:text-lg group-hover:text-amber-1000">{{ $nextEntry->title ?? '' }}</p>
            </div>
            <div class="ds-prev-next-nav__next--arrow">
                <i class="fa-sharp fa-light fa-arrow-right-long"></i>
            </div>
        </a>
    @endif
    
</nav>