@php
// Pass through a collection of $entries (ideal for additional filtering) or remove to use the current collection
$entries = Statamic\Facades\Entry::query()
    ->where('collection', 'projects')
    ->where('published', true)
    ->where(function($query) {
        $query->where('type', 'project')
            ->orWhere('type', 'both');
    })
    ->orderBy('completition_date', 'asc')
    ->get();
@endphp

<x-prev-next-nav :entryId="$page->id" :entries="$entries" icon="fa-solid fa-grid-2" back="/our-projects/featured-projects" />