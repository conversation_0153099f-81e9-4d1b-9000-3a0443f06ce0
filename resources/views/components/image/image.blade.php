@props([
   'type' => '',
   'src' => 'https://picsum.photos/600',
   'alt' => 'Image',
   'glideWidth' => null
])


<div x-data="Image" data-module="ds-image" {{ $attributes->merge(['class' => 'ds-image ds-image--'.$type. '']) }}>
   @if($glideWidth)
    @foreach (Statamic::tag('glide:generate')->src($src)->width($glideWidth) as $image)
    <img src="{{ $image['url'] }}" alt="{{ $image['alt'] ?? '' }}" class="object-cover w-full h-full">
    @endforeach
    @else
    <img src="{{ $src }}" alt="{{ $alt }}">
    @endif
</div>