@props([
    'eyebrow' => '',
    'title' => '',
    'description' => '',
    'image' => '',
])

<div x-data="PageTitle" data-module="ds-page-title" {{ $attributes->merge(['class' => 'ds-page-title']) }}>
    <div class="relative z-20 overflow-hidden">
            <p class="ds-page-title__eyebrow">{!! $eyebrow ?? '' !!}</p>
            <h1 class="ds-page-title__title !leading-tight">{!! $title ?? '' !!}</h1>
            {{ $slot }}
    </div>
    <div class="ds-page-title__image">
        @foreach (Statamic::tag('glide:generate')->src($image ?? '')->width(1200)->height(600)->fit('crop_focal') as $image)
            <img src="{{ $image['url'] }}" alt="">
        @endforeach
    </div>
    <div class="ds-page-title__overlay"></div>
</div>