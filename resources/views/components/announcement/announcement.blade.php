@props(
    [
        'content' => $content ?? null,
        'title' => null,
        'fullWidth' => false,
        'containerClasses' => 'container mx-auto',
        'componentContainerClasses' => 'container mx-auto',
    ]
)
<div x-data="Announcement" data-module="ds-announcement" {{ $attributes->merge(['class' => 'ds-announcement '.($fullWidth ? $componentContainerClasses : $containerClasses).'']) }}>
    @if(isset($title))
        <h2 class="ds-announcement__title">{{ $title ?? '' }}</h2>
    @endif
    <div class="ds-announcement__content">
        {{ $slot }}
    </div>
</div>