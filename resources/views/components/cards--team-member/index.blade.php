@props(
    [
        'member' => [],
    ]
)
@foreach (Statamic::tag('glide:generate')->src($member->profile_image)->width(800) as $image)
    @php
        $profile_image_url = $image['url'];
    @endphp
@endforeach
<li x-data="CardsTeamMember" data-module="ds-cards--team-member"
    {{ $attributes->merge(['class' => 'ds-cards--team-member']) }}>
    <div class="ds-cards--team-member__image-container">
        <button x-on:click="enableBodyLock(), profile_image = '{{ $profile_image_url ?? '' }}', name = '{{ $member->title ?? '' }}', position = '{{ $member->position ?? '' }}', email = '{{ $member->email ?? '' }}', phone = '{{ $member->phone ?? '' }}', linkedin_profile = '{{ $member->linkedin_profile ?? '' }}', biography = '{{ $member->biography ?? '' }}', teamoverlay = !teamoverlay" class="ds-cards--team-member__image-container--overlay">
            <div class="ds-cards--team-member__image-container--arrow">
                <i class="fa-sharp fa-regular fa-arrow-right-long"></i>
            </div>
        </button>
        @foreach (Statamic::tag('glide:generate')->src($member->profile_image)->width(600) as $image)
            <img src="{{ $image['url'] }}" width="600" class="ds-cards--team-member__image-container--image" />
        @endforeach
    </div>
    <div class="ds-cards--team-member__content">
        <div>
            <h3 class="ds-cards--team-member__content--title">
                {{ $member->title ?? '' }}
            </h3>
            <p class="ds-cards--team-member__content--position">{{ $member->position ?? '' }}</p>
        </div>
        <div class="flex justify-center gap-x-3">
            @isset($member->linkedin_profile)
                <a href="{{ $member->linkedin_profile ?? '' }}" target="_blank" class="ds-cards--team-member__content--link">
                    <i class="fa-brands fa-linkedin-in" aria-hidden="true"></i>
                </a>
            @endisset
            @isset($member->email)
                <a href="mailto:{{ $member->email ?? '' }}" class="ds-cards--team-member__content--link">
                    <i class="fa-solid fa-envelope" aria-hidden="true"></i>
                </a>
            @endisset
            @isset($member->phone)
                <a href="tel:{{ $member->phone ?? '' }}" class="ds-cards--team-member__content--link">
                     <i class="fa-solid fa-phone" aria-hidden="true"></i>
                </a>
            @endisset
        </div>

        <x-button x-on:click="enableBodyLock(), profile_image = '{{ $profile_image_url }}', name = '{{ $member->title ?? '' }}', position = '{{ $member->position ?? '' }}', email = '{{ $member->email ?? '' }}', phone = '{{ $member->phone ?? '' }}', linkedin_profile = '{{ $member->linkedin_profile ?? '' }}', biography = '{{ $member->biography ?? '' }}', teamoverlay = !teamoverlay" class="w-full">
            View profile
        </x-button>
    </div>
</li>