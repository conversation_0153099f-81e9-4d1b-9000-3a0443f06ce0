.ds-cards--team-member {
    @apply flex flex-col bg-white rounded-lg px-4 pt-4 pb-6 duration-200 hover:shadow-[0_20px_40px_-20px_rgba(0,_0,_0,_0.35)] hover:-translate-y-1;
    &__image-container {
        @apply relative aspect-square rounded overflow-hidden bg-gradient-to-t from-neutral-100 to-transparent;
        &--overlay {
            @apply absolute rounded inset-0 bg-neutral-600 bg-opacity-0 hover:bg-opacity-60 duration-200;
        }
        &--arrow {
            @apply absolute flex items-center justify-center inset-0 -translate-x-6 hover:translate-x-0 duration-500 opacity-0 hover:opacity-100 text-2xl text-white z-10;
        }
        &--image {
            @apply object-cover mix-blend-multiply;
        }
    }
    &__content {
        @apply mt-4 text-center flex-grow flex flex-col justify-between space-y-4;
        &--title {
            @apply font-medium text-2xl text-neutral-600;
        }
        &--position {
            @apply text-neutral-400 font-medium;
        }
        &--link {
            @apply flex items-center justify-center rounded-full h-9 w-9 text-red-600 bg-[#FEEAEE] hover:bg-[#f8dae0] duration-200;
        }
    }
}