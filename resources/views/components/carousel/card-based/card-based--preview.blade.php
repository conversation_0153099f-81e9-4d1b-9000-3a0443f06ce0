@php
    $slides = [
        [
            'title' => 'Fishing',
            'image' => 'https://picsum.photos/640/640?random=1',
            'image_alt' => 'Fishing',
            'href' => 'http://www.example.com'
        ],
        [
            'title' => 'Boating',
            'image' => 'https://picsum.photos/640/640?random=2',
            'image_alt' => 'Boating',
            'href' => 'http://www.example.com'
        ],
        [
            'title' => 'Bushcraft',
            'image' => 'https://picsum.photos/640/640?random=3',
            'image_alt' => 'Bushcraft',
            'href' => 'http://www.example.com'
        ],
        [
            'title' => 'Yoga',
            'image' => 'https://picsum.photos/640/640?random=4',
            'image_alt' => 'Yoga',
            'href' => 'http://www.example.com'
        ]
    ]
@endphp

<x-carousel.card-based cardType="card.image" :slides="$slides" hidePrevNextBtns cardClasses="ds-card--full-image--square" class="mb-10 explore-carousel carousel--card-based--with-overflow">
    <div class="custom-nav">
        <button class="button button--previous">
            <i class="fa-solid fa-angle-left"></i>
        </button>
        <button class="button button--next">
            <i class="fa-solid fa-angle-right"></i>
        </button>
    </div>
</x-carousel.card-based>

<!-- Only required for this preview -->
<style>
    .carousel-cell {
        max-width: 250px;
    }
</style>