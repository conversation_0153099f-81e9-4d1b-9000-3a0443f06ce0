slug: card-based
name: Card based
parent: Carousels
status: wip
parameters_instructions: The carousel component uses the Flickty JS library. Full instructions can be found at <a href="https://flickity.metafizzy.co" target="_blank">https://flickity.metafizzy.co</a>
parameters:
  -
    name: slides
    type: array
    required: true
    notes: i.e. [['title' => 'Slide 1', 'img' => '/img/slide/slide-1.png'], ['title' => 'Slide 2', 'img' => '/img/slide/slide-2.png']]
  -
    name: cardType
    type: string
    default: card--full-image
  -
    name: cardClasses
    type: string
    notes: Any classes that should be applied to the carousel cards
  -
    name: hidePrevNextBtns
    type: parameter
  -
    name: pageDots
    type: parameter
  -
    name: autoPlay
    type: parameter
  -
    name: groupCells
    type: string
    notes: Provide 3 values for the small, medium and large breakpoints i.e. groupCells="1,2,3"
    link: https://flickity.metafizzy.co/options.html#groupcells
classes:
  -
    name: carousel--card-based--with-overflow