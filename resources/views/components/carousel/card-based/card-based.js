export default () => ({
    init() {
        if (!this.$root.classList.contains('ignore-js')) {
            this.$carousel_element = this.$root.querySelector('div:first-of-type');

            if (!this.$carousel_element) {
                console.error("Flickity: Carousel element not found!", this.$root);
                return;
            }

            const slideCount = this.$carousel_element.querySelectorAll('.carousel-cell').length;
            const showArrows = slideCount > 1 && !this.$root.hasAttribute("hideprevnextbtns");

            var options = {
                prevNextButtons: this.$root.querySelector(".custom-nav") ? false : showArrows,
                pageDots: this.$root.hasAttribute("pagedots"),
                autoPlay: this.$root.hasAttribute("autoplay") ? 3000 : false, // ✅ Ensure autoplay is in milliseconds
                fade: this.$root.hasAttribute("fade"),
                cellAlign: 'left',
                wrapAround: true, // ✅ Enables infinite scrolling
                contain: false, // ❌ Must be false for `wrapAround` to work
                imagesLoaded: true,
                freeScroll: true, // ✅ Allows smooth scrolling without snapping
                initialIndex: this.$root.hasAttribute("initialindex") 
                    ? parseInt(this.$root.getAttribute("initialindex"), 10) 
                    : 0, // ✅ Read from attribute or default to 0
            };

            // ✅ Fix groupCells
            if (this.$root.hasAttribute("groupcells")) {
                var groups = this.$root.getAttribute("groupcells").split(',');

                let updateGroupCells = () => {
                    let width = window.innerWidth;
                    if (width >= 1024) {
                        options.groupCells = parseInt(groups[2]); // ✅ Ensure it's an integer
                    } else if (width >= 640) {
                        options.groupCells = parseInt(groups[1]);
                    } else {
                        options.groupCells = parseInt(groups[0]);
                    }
                };

                updateGroupCells();
                window.addEventListener("resize", () => {
                    updateGroupCells();
                    flkty.options.groupCells = options.groupCells;
                    flkty.reloadCells(); // ✅ Reload Flickity cells after updating
                });
            }

            let flkty = new Flickity(this.$carousel_element, options);

            if (this.$root.querySelector(".custom-nav")) {
                if (this.$root.querySelector(".button--previous") && this.$root.querySelector(".button--next")) {
                    this.$root.querySelector(".button--next").addEventListener("click", () => {
                        flkty.next();
                    });
                    this.$root.querySelector(".button--previous").addEventListener("click", () => {
                        flkty.previous();
                    });
                }

                var progressBar = this.$root.querySelector('.progress-bar');
                if (progressBar) {
                    flkty.on('scroll', function (progress) {
                        progress = Math.max(0, Math.min(1, progress));
                        progressBar.style.width = progress * 100 + '%';
                    });
                }
            }

            // ✅ Fix: Force Flickity to resize after images load
            setTimeout(() => {
                flkty.resize();
            }, 500);
        }
    },
});
