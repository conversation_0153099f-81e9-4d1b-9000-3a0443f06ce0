[data-module="ds-carousel--card-based"] {
    @apply relative ring-0 focus:ring-0 focus-visible:ring-0 focus-visible:outline-none;
    .carousel-cell {
        @apply mx-4 w-full;
        -webkit-transform-style: preserve-3d;
        transform-style: preserve-3d;
    }
    .custom-nav {
        @apply absolute bottom-6 lg:bottom-8  space-x-3;
        .button {
            @apply duration-200 text-white bg-transparent hover:bg-transparent hover:text-stone-200 text-[56px] rounded-none h-auto;
        }
    }
   &.carousel--card-based--with-overflow {
        .flickity-viewport {
            @apply overflow-visible;
        }
   }
   .flickity-page-dots {
    @apply absolute mx-0 z-10 bottom-10 flex justify-center inset-x-0 space-x-5;
        .dot {
            @apply rounded-full h-3 w-3 bg-white/50 hover:bg-white/80 duration-200 last:mr-0;
            &.is-selected {
            @apply bg-yellow-150;
            }
        }
   }
   .flickity-viewport, .flickity-slider, .flickity-enabled {
        @apply ring-0 focus:ring-0 focus-visible:ring-0 focus-visible:outline-none;
   }
//    .ds-card--full-image {
//         @apply aspect-3/2 rounded-none h-full min-h-[100px];
//         &__image {
//             @apply rounded-t-lg rounded-b-none;
//         }
//         &__bg-gradient {
//             @apply rounded-t-lg rounded-b-none;
//         }
//    }
}
.ds-card--horizontal {
    [data-module="ds-carousel--card-based"] {
        .ds-card--full-image {
            &__image {
                @apply md:rounded-tr-none;
            }
        }
    }
}