@props([
	'slides' => [],
	'cardType' => 'card.default',
	'cardClasses' => '',
	'glideWidth' => null,
	'glideHeight' => null
])

<div x-data="CardBased" data-module="ds-carousel--card-based" {{ $attributes->merge(['class' => 'ds-carousel--card-based']) }}>
	<div>
		@foreach($slides as $item)
		<div class="carousel-cell group">
			<x-dynamic-component :component="$cardType" :item="$item" :glideWidth="$glideWidth" :glideHeight="$glideHeight" :class="$cardClasses">
				<x-slot:title>
					{{ $slide['title'] ?? '' }}
				</x-slot>
				{{ $slide['content'] ?? '' }}
			</x-dynamic-component>
		</div>
		@endforeach
	</div>
	{{ $slot }}
</div>
