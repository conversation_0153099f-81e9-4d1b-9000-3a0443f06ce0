@props([
'slides' => [],
'cardType' => 'cards--project',
'cardClasses' => ''
])

<div x-data="CarouselCardBased" data-module="ds-carousel--card-based-2" {{ $attributes->merge(['class' => 'ds-carousel--card-based-2 swiper']) }}>
	<div class="swiper-wrapper">
		@foreach($slides as $item)
		<div class="swiper-slide group">
			<x-dynamic-component :component="$cardType" :item="$item" :class="$cardClasses">
				<x-slot:title>
					{{ $slide['title'] ?? '' }}
					</x-slot>
					{{ $slide['content'] ?? '' }}
			</x-dynamic-component>
		</div>
		@endforeach
	</div>
	{{ $slot }}
</div>
