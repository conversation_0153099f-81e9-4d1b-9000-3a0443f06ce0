<div data-module="ds-gallery" {{ $attributes->merge(['class' => 'ds-gallery']) }}>
    @foreach ($images as $image)
        @php
            // Ensure the asset is properly resolved
            $glideUrl = $image->url(['w' => 1200]);
            $thumbnailUrl = $image->url(['w' => 500, 'fit' => 'crop_focal']);
        @endphp
        <a data-fancybox="gallery" class="relative group" href="{{ $glideUrl }}">
            <div
                class="absolute flex items-center justify-center w-full h-full duration-300 group-hover:bg-[#1A3550]/60">
                <span
                    class="text-[44px] text-white transition ease-in-out -translate-y-6 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 duration-300">
                    <i class="fa-thin fa-circle-plus"></i>
                </span>
            </div>
            <img src="{{ $thumbnailUrl }}" alt="{{ $image->meta('alt') ?? $image->basename() }}" class="aspect-3/2" />
        </a>
    @endforeach
</div>
