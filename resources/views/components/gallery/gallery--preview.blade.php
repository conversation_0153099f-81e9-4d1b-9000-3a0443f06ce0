@php
    $items = [
        [
            'title' => 'Accordion 1',
            'content' => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit
            </p>',
        ],
        [
            'title' => 'Accordion 2',
            'content' => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit
            </p>',
        ],
        [
            'title' => 'Accordion 3',
            'content' => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit
            </p>',
        ],
    ]
@endphp
@foreach($items as $index => $item)
    <x-accordion :count="$loop->iteration" icon="fa-sharp fa-solid fa-plus" :title="$item['title']" class="w-full"> 
        <div class="text-block lg:text-lg">
            {!! $item['content'] !!}
        </div>
    </x-accordion>
@endforeach