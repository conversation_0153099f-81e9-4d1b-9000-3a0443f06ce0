@props(
    [
        'content' => $content ?? null,
        'title' => null,
    ]
)
<section x-data="News-feature-grid" data-module="ds-news-feature-grid" {{ $attributes->merge(['class' => 'ds-news-feature-grid']) }}>
    <div class="ds-site-padding">
        <div class="ds-news-feature-grid__container-grid">
            <div>
                @php
                    $data = [
                        'news' => [
                            ["type" => "feature", "href" => "/news-article", "image" => "https://unsplash.it/600/400", "title" => "Nitrite-free bacon tastes and looks better, so why do we ignore the risks?", "description" => "The UK only has one manufacturer that produces nitrite-free bacon and its lone competitor vanished without trace.", "date" => "3 hrs ago"]
                        ]
                    ];
                @endphp
                @foreach($data['news'] as $item)
                    <x-card.news :item="$item" :type="$item['type']" />
                @endforeach
            </div>
            <ul role="list" class="ds-news-feature-grid__primary-items">
                @php
                    $data = [
                        'news' => [
                            ["type" => "primary", "href" => "/news-article", "image" => "https://unsplash.it/600/400", "title" => "The truth about nitro-meats: my seven-year search for better bacon", "date" => "12 hrs ago"],
                            ["type" => "primary", "href" => "/news-article", "image" => "https://unsplash.it/600/400", "title" => "How removing nitrites from processed meats can reduce rates of colorectal cancer in the UK", "date" => "10 Feb 2025"],
                            ["type" => "primary", "href" => "/news-article", "image" => "https://unsplash.it/600/400", "title" => "Labour MP Kerry McCarthy, backs the science and urges the government to act now", "date" => "06 Feb 2025"]
                        ]
                    ];
                @endphp
                @foreach($data['news'] as $item)
                    <li>
                        <x-card.news :item="$item" :type="$item['type']" />
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
</section>