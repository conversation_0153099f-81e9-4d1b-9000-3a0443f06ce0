@props([
	'element' => 'tabs',
	'tabs' => [],
	'active' => null,
	'id' => null,
	'placeholder' => $placeholder ?? 'Select an option',
	'force_reload' => $force_reload ?? false,
])

@if ($element === 'tabs')
	<div x-data="Tabs('{{ $id }}')" id="{{ $id }}" data-module="ds-tabs" data-type="tabs" {{ $attributes->merge(['class' => 'ds-tabs']) }}>
		<div data-type="tabs">
			<div class="ds-tabs--mobile">
				<label for="tabs" class="sr-only">Select a tab</label>
				{{-- <x-form-element.select :items="$tabs" :placeholder="$placeholder ?? ''" :force_reload="$force_reload ?? false" /> --}}
			</div>
			<div class="ds-tabs--desktop">
				<div class="border-b border-neutral-300">
					<nav class="ds-tabs--desktop__container" aria-label="Tabs">
						@foreach ($tabs as $tab)
							@if(isset($tab['href']) && $force_reload!=="true")
								<a href="{{ $tab['href'] }}" class="ds-tabs--desktop__tab @isset($tab['active'])active @endisset" data-slug="{{ $tab['slug'] ?? '' }}">
									{{ $tab['title'] }}
								</a>
							@else
								<button x-on:click="activeTabViaUrl = '', activeTab = '{{ $tab['id'] }}'" @if($loop->index==0) x-ref="default-tab" @endif data-id="{{ $tab['id'] }}" class="ds-tabs--desktop__tab"
									:class="activeTabViaUrl != '' && activeTabViaUrl == '{{ $tab['id'] }}' ? 'active' : (activeTab == '{{ $tab['id'] }}'  ? 'active' : '')" role="tab">
									{{ $tab['title'] }}
								</button>
							@endif
						@endforeach
					</nav>
				</div>
			</div>
		</div>
		{{ $slot }}
	</div>
@endif

@if ($element === 'panel')	
	<div :class="activeTabViaUrl=='' ? (activeTab == '{{ $id ?? '' }}' ? '' : 'hidden') : (activeTabViaUrl == '{{ $id }}' ? '' : 'hidden')" @if($active!==null) x-init="if(activeTabViaUrl==''){ activeTab = '{{ $id }}'; }" @endif x-cloak data-type="panel" {{ $attributes->merge(['class' => '']) }}>
		{{ $slot }}
	</div>
@endif