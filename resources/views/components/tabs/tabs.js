export default (id) => ({
    init(){
        // console.log('tabs.js alpine loaded with an id of ' + id);
        this.$watch('activeTab', function(value){
            window.dispatchEvent(new Event('resize'));
        });
        if(id==''){
            console.log("%cDS ERROR: No ID set on the tabs container", "font-weight: bold; color:cornflowerblue; padding-left: 4px; border-left: 2px solid hotpink;");
        }else{
            if(document.querySelector("#"+id).getAttribute('selector')=='segment'){
                var segments = window.location.href.split('/');
                var lastSegment = segments.pop() || segments.pop();
                lastSegment = lastSegment.split('#')[0];
                document.querySelector("#"+id+" .ds-tabs--desktop__tab[data-slug='"+lastSegment+"']").classList.add('active');
            }
        }
        window.onhashchange = function() {
            this.activeTab = window.location.hash.replace('#', '');
            document.querySelector("#"+id+" .ds-tabs--desktop__tab[data-id='"+this.activeTab+"']").click();
        }
    },
    activeTabViaUrl: window.location.hash.replace('#', ''),
    activeTab: () => {
        if(window.location.hash.replace('#', '') != ''){
            return window.location.hash.replace('#', '');
        }
        if(document.querySelector("#"+id+" .ds-tabs--desktop__tab")){
            return document.querySelector("#"+id+" .ds-tabs--desktop__tab").getAttribute('data-id');
        }
        return null;
    },
})