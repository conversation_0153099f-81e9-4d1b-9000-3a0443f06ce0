@props([
   'type' => 'feature-1',
   'icon' => null,
   'title' => '',
])

<div data-module="ds-feature" {{ $attributes->merge(['class' => 'ds-feature ds-feature--custom ds-feature--'.$type]) }}>
   <div class="flex items-start mb-4">
      <div class="ds-feature__icon-container">
         <div class="ds-feature__icon">
            @if (Str::startsWith($icon, 'fa-'))
               <i class="{{ $icon }}"></i>
            @else
               {!! $icon !!}
            @endif
         </div>
      </div>
      <h3>{{ $title ?? '' }}</h3>
   </div>
   {{ $slot }}
</div>