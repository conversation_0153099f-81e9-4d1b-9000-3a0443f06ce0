.ds-header {
    @apply absolute w-full py-6 md:py-2.5 z-50 duration-100;
        @media (min-width: 1280px) {
            padding-top: 1.19rem;
            padding-bottom: 1.19rem;
        }
        .hamburger-line {
            @apply bg-yellow-150;
        }
        .master-logo {
            @apply w-[190px] xl:w-auto;
            display: block !important;
        }
        &.active {
            @apply fixed top-0 left-0 py-6 md:py-2.5 bg-sage-500 z-50 shadow-sm;
        }
        .desktop-menu {
            li {
                @apply flex flex-col items-start;
                a, button {
                    @apply font-sans tracking-wider text-white hover:text-yellow-150 uppercase text-sm duration-200;
                }
                .underline {
                    @apply block h-px bg-yellow-150 w-0 duration-200 ease-in-out;
                }
                .active-nav-item {
                    @apply text-white;
                    .underline {
                        width: 100% !important;
                    }
                }
            }
        }
    .mobile-menu {
        ul {
            @apply mt-12 space-y-3 md:mt-0 md:space-y-4 flex-grow;
            li {
                @apply overflow-hidden;
                svg {
                    @apply text-red-650 w-4 h-4;
                }
                a, button {
                    @apply relative inline-flex items-center text-white hover:text-yellow-150 duration-200 ease-in-out opacity-0 animate-mask-fade-up animation-delay-100 font-sans uppercase tracking-wider text-lg w-full;
                    .animated-icon {
                        @apply text-base font-medium duration-200 -translate-x-4 md:text-lg md:-translate-x-6 mr-2 text-red-650 -mt-1;
                    }
                    .text {
                        @apply text-2xl font-medium duration-200 -translate-x-4 md:text-3xl md:-translate-x-6;
                    }
                    .chevron {
                        @apply absolute right-0 text-xl text-gray-300/70 duration-200 ease-in-out;
                    }
                    &.group:hover {
                        .animated-icon {
                            @apply translate-x-2;
                        }
                        .text {
                            @apply text-red-650 translate-x-2;
                        }
                        .chevron {
                            @apply text-red-650;
                        }
                    }
                }
            }
        }
        .mobile-submenu {
            @apply mt-2 py-3 space-y-3;
            li {
                a {
                    @apply capitalize text-lg font-medium;
                    span {
                        @apply w-6 text-gray-400 text-sm -mt-1;
                    }
                }
            }
        }
        .divider {
            @apply h-px opacity-0 bg-neutral-300/20 animate-in-from-left animation-delay-100;
        }
    }
}