<div x-data="Hero" data-module="ds-hero" {{ $attributes->merge(['class' => 'ds-hero']) }}>
      <div class="inset-x-0 ds-hero__desktop ds-site-padding">
         <div class="w-2/3">
            <div class="mb-4 overflow-hidden">
               <h2 class="ds-hero__desktop--eyebrow animate-mask-fade-up animation-delay-0">{{ $eyebrow ?? '' }}</h2>
            </div>
            <div data-aos="fade-up" data-aos-duration="1000">
               <div class="ds-hero__desktop--description">{!! $description ?? '' !!}</div>
            </div>
         </div>
      </div>
      <div class="hidden lg:block absolute inset-0 bg-[radial-gradient(circle_at_50%_16%,_rgba(1,_5,_32,_0),_rgba(9,_13,_47,_0.38)_71%,_#000_110%);]">
      </div>
      <div class="absolute inset-0 hidden opacity-50 lg:block bg-neutral-600"></div>
      <img src="{{ $image ?? '' }}" alt="{{ $eyebrow ?? '' }}" class="ds-hero__image" />
</div>
<div class="ds-hero__mobile ds-section-padding-t ds-site-padding">
      <div class="mb-2 overflow-hidden">
         <h2 class="ds-hero__mobile--eyebrow animate-mask-fade-up animation-delay-0">{{ $eyebrow ?? '' }}</h2>
      </div>
      <div data-aos="fade-up" data-aos-duration="1000">
         <p class="ds-hero__mobile--description">{{ $description ?? '' }}
         </p>
      </div>      
</div>