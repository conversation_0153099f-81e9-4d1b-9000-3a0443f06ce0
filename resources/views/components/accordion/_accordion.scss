.ds-accordion {
    &__container {
        @apply relative border-b border-gray-200 break-inside-avoid;
    }
    &__button {
        @apply w-full py-3 lg:py-5 text-left;
        &--inner {
            @apply flex items-center justify-between px-2 lg:px-4 space-x-6;
            &--title {
                @apply text-xl md:text-2xl lg:text-2xl font-black text-gray-950;
            }
        }
        &--icon-container {
            @apply flex items-center justify-center rounded-full h-9 w-9 duration-200 flex-shrink-0;
        }
        &--icon {
            @apply text-sm duration-200 ease-in-out transform;
        }
    }
    &__content {
        @apply relative overflow-hidden transition-all max-h-0 duration-500;
        &--inner {
            @apply px-2 lg:px-4 pb-6 lg:pb-10;
        }
    }
    ul {
        @apply space-y-1;
        li {
            @apply flex items-center;
            &::before {
                @apply mr-2 text-2xl;
                font-family: 'Font Awesome 6 Sharp';
                content: '\f178';
                font-size: .875rem;
                margin-right: 10px;
                line-height: 1.25rem;
                --tw-text-opacity: 1;
                color: rgb(196 3 40 / var(--tw-text-opacity));
            }
        }
    }
}