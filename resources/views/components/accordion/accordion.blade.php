@props([
    'item' => [],
    'title' => '', // Default to empty string to prevent undefined variable errors
    'icon' => null, // Default to null to handle optional icon
])

<div data-module="ds-accordion" {{ $attributes->merge(['class' => 'ds-accordion']) }}>
    <div class="ds-accordion__container group" x-cloak>
        <button type="button" class="ds-accordion__button"
            x-on:click="accordion_selected !== '{{ Illuminate\Support\Str::slug($title ?? '', '_') }}' ? accordion_selected = '{{ Illuminate\Support\Str::slug($title ?? '', '_') }}' : accordion_selected = null"
            :class="accordion_selected == '{{ Illuminate\Support\Str::slug($title ?? '', '_') }}' ? '' : 'group-hover:bg-gray-50 duration-200'"
            data-accordion-item="{{ Illuminate\Support\Str::slug($title ?? '', '_') }}">
            <div class="ds-accordion__button--inner">
                <div class="flex items-center space-x-4 lg:space-x-8">
                    <span class="ds-accordion__button--inner--title">{{ $title }}</span>
                </div>
                @if($icon)
                <div class="ds-accordion__button--icon-container"
                    :class="accordion_selected == '{{ Illuminate\Support\Str::slug($title ?? '', '_') }}' ? 'transform !bg-red-650' : 'bg-red-650 group-hover:bg-red-500'">
                    <i class="{{ $icon }} ds-accordion__button--icon"
                        :class="accordion_selected == '{{ Illuminate\Support\Str::slug($title ?? '', '_') }}' ? 'transform ease-in-out rotate-45 !text-white bg-red-650' : 'text-white'"></i>
                </div>
                @endif
            </div>
        </button>

        <div class="ds-accordion__content" x-ref="container_{{ Illuminate\Support\Str::slug($title ?? '', '_') }}"
            x-bind:style="accordion_selected == '{{ Illuminate\Support\Str::slug($title ?? '', '_') }}' ? 'max-height: ' + $refs.container_{{ Illuminate\Support\Str::slug($title ?? '', '_') }}.scrollHeight + 'px' : ''">
            <div class="ds-accordion__content--inner">
                {{ $slot }}
            </div>
        </div>
    </div>
</div>
