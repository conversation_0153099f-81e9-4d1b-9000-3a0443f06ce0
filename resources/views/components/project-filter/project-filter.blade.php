@props([])

<div data-module="ds-project-filter" {{ $attributes->merge(['class' => 'ds-project-filter']) }}>

    @php
    $waterProjects = Statamic::tag('collection:projects')
    ->params(['categories:contains' => 'water-source-heat-pumps'])->fetch();
    @endphp

    <div class="ds-site-padding">
        <div class="p-4 mb-6 space-y-4 bg-white border rounded-lg">
            <div class="pb-4 border-b border-dotted md:flex md:space-x-4 md:items-center">
                <div class="mb-2 md:mb-0 md:mt-2">
                    <label class="self-center min-w-[50px] text-sm md:text-base">
                        Project:
                    </label>
                </div>
                
                <div class="items-end space-y-2 md:flex md:flex-wrap md:space-x-2">
                    <button x-on:click="filter1.includes('commercial') ? filter1.splice(filter1.indexOf('commercial'), 1) : filter1.push('commercial')" :class="filter1.indexOf('commercial')==-1 ? '' : 'bg-orange-100 !border-orange-500'" data-module="ds-button" class="ds-button w-full sm:w-auto !justify-start sm:!justify-center group ds-button--orange__icon--prepend">
                        <i class="mx-2" :class="filter1.indexOf('commercial')==-1 ? 'fa-regular fa-square' : 'fa-duotone fa-square-check text-orange-500'"></i>
                        Commercial
                    </button>
                    <button x-on:click="filter1.includes('domestic') ? filter1.splice(filter1.indexOf('domestic'), 1) : filter1.push('domestic')" :class="filter1.indexOf('domestic')==-1 ? '' : 'bg-blue-100 !border-blue-500'" data-module="ds-button" class="ds-button w-full sm:w-auto !justify-start sm:!justify-center group ds-button--blue__icon--prepend">
                        <i class="mx-2" :class="filter1.indexOf('domestic')==-1 ? 'fa-regular fa-square' : 'fa-duotone fa-square-check text-blue-500'"></i>
                        Domestic
                    </button>
                </div>
            </div>


            <div class="md:flex md:space-x-4 md:items-center">
                <div class="mb-2 md:mb-0 md:mt-2">
                    <label class="self-center min-w-[50px] text-sm md:text-base">
                        Source:
                    </label>
                </div>
                <div class="items-end space-y-2 md:flex md:flex-wrap md:space-x-2">
                    <button x-on:click="filter2.includes('air-source-heat-pumps') ? filter2.splice(filter2.indexOf('air-source-heat-pumps'), 1) : filter2.push('air-source-heat-pumps')" :class="filter2.indexOf('air-source-heat-pumps')==-1 ? '' : 'bg-green-100 !border-green-500'" data-module="ds-button" class="ds-button w-full sm:w-auto !justify-start sm:!justify-center group ds-button--green__icon--prepend">
                        <i class="mx-2" :class="filter2.indexOf('air-source-heat-pumps')==-1 ? 'fa-regular fa-square' : 'fa-duotone fa-square-check text-green-500'"></i>
                        Air source heat pumps
                    </button>
                    <button x-on:click="filter2.includes('ground-source-heat-pumps') ? filter2.splice(filter2.indexOf('ground-source-heat-pumps'), 1) : filter2.push('ground-source-heat-pumps')" :class="filter2.indexOf('ground-source-heat-pumps')==-1 ? '' : 'bg-yellow-100 !border-yellow-500'" data-module="ds-button" class="ds-button w-full sm:w-auto !justify-start sm:!justify-center group ds-button--yellow__icon--prepend">
                        <i class="mx-2" :class="filter2.indexOf('ground-source-heat-pumps')==-1 ? 'fa-regular fa-square' : 'fa-duotone fa-square-check text-yellow-500'"></i>
                        Ground source heat pumps
                    </button>
                    @if($waterProjects->count() > 0)
                    <button x-on:click="filter2.includes('water-source-heat-pumps') ? filter2.splice(filter2.indexOf('water-source-heat-pumps'), 1) : filter2.push('water-source-heat-pumps')" :class="filter2.indexOf('water-source-heat-pumps')==-1 ? '' : 'bg-sky-100 !border-sky-500'" data-module="ds-button" class="ds-button w-full sm:w-auto !justify-start sm:!justify-center group ds-button--sky__icon--prepend">
                        <i class="mx-2" :class="filter2.indexOf('water-source-heat-pumps')==-1 ? 'fa-regular fa-square' : 'fa-duotone fa-square-check text-sky-500'"></i>
                        Water source heat pumps
                    </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>