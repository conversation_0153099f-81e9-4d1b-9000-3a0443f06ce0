@props([
	'item' => [],
])
<div x-data="Call-out-box" data-module="ds-call-out-box" {{ $attributes->merge(['class' => 'ds-call-out-box']) }}>

        <div class="absolute inset-0 opacity-15"><img src="{{ $item['bg_image'] ?? '' }}" class="object-cover w-full h-full"></div>
        <div class="relative max-w-4xl mx-auto flex items-center flex-col dark">
            @if(isset($item['eyebrow']))
                <h4 class="eyebrow">{{ $item['eyebrow'] ?? '' }}</h4>
            @endif
            <p class="text-xl md:text-2xl lg:text-3xl font-black">
                {{ $slot }}
            </p>
            <x-button href="{{ $item['button_url'] ?? '' }}" type="secondary" class="mt-6 lg:mt-8">{{ $item['button_label'] ?? '' }}</x-button>
        </div>
   
</div>