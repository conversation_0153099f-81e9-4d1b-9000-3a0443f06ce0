@props([
    'eyebrow' => [],
    'title' => '',
    'description' => '',
])

<div x-data="SectionHeaderLight" data-module="ds-section-header-light" {{ $attributes->merge(['class' => 'ds-section-header-light']) }}>
    <img class="absolute top-0 left-0 w-full animate-pulse" src="/img/section-header-blur-3.svg" />
    <svg class="absolute inset-0 h-full w-full stroke-white opacity-50 [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]" aria-hidden="true">
        <defs>
            <pattern id="0787a7c5-978c-4f66-83c7-11c213f99cb7" width="70" height="70" x="50%" y="-1" patternUnits="userSpaceOnUse">
                <path d="M.5 200V.5H200" fill="none" />
            </pattern>
        </defs>
        <rect width="100%" height="100%" stroke-width="0" fill="url(#0787a7c5-978c-4f66-83c7-11c213f99cb7)" />
    </svg>
    @if(isset($eyebrow['text']))
        <div class="relative overflow-hidden flex items-start">
            <x-eyebrow class="relative ds-eyebrow ds-eyebrow--light animate-mask-fade-up animation-delay-100 z-20 mb-4 lg:mb-6" :icon="$eyebrow['icon'] ?? ''" >
                {{ $eyebrow['text'] ?? '' }}
            </x-eyebrow>
        </div>
    @endif
     <div class="relative overflow-hidden z-20">
        <h1 class="ds-section-header-light__title !leading-tight animate-mask-fade-up">{{ $title ?? '' }}</h1>
    </div>
    @if(isset($description) && $description !== '')
        <div class="relative overflow-hidden z-20">
            <div class="ds-section-header-light__description animate-mask-fade-up animation-delay-100">{!! $description ?? '' !!}</div>
        </div>
    @endif
</div>