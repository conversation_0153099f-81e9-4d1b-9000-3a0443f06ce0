@props([
    'type' => 'light',
    'title' => null,
    'icon' => null
])

<div x-data="Eyebrow" data-module="ds-eyebrow" {{ $attributes->merge(['class' => 'ds-eyebrow ds-eyebrow--'.$type]) }}>
    <div class="ds-eyebrow__inner">
        <div class="ds-eyebrow__icon">   
            @if (Str::startsWith($icon, 'fa'))
                <i class="{{ $icon }}"></i>
            @else
                {!! $icon !!}
            @endif
        </div>
        {{ $title ?? $slot }}
    </div>
</div>