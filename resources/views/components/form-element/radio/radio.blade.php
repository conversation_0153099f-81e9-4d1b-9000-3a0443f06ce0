@props(
    [   
        'id' => null,
        'aria' => null,
        'name' => null,
        'label' => null,
        'value' => null,
    ]
)
<div x-data="Radio" data-module="ds-radio" {{ $attributes->merge(['class' => 'ds-radio']) }}>
    <label class="relative flex items-start">
        <div class="flex items-center h-6">
            <input id="{{ $id ?? '' }}" aria="{{ $aria ?? '' }}" name="{{ $name ?? '' }}" type="radio" value="{{ $value ?? '' }}" >
        </div>
        <div class="ml-3 text-base/6">
            {{ $label ?? '' }}</label>
        </div>
    </label>
</div>