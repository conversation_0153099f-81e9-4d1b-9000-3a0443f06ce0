@props(
    [   
        'id' => null,
        'aria' => null,
        'name' => null,
        'label' => null,
    ]
)
<div x-data="Checkbox" data-module="ds-checkbox" {{ $attributes->merge(['class' => 'ds-checkbox']) }}>
    <div class="relative flex items-start">
        <div class="flex h-6 items-center">
            <input id="{{ $id ?? '' }}" aria="{{ $aria ?? '' }}" name="{{ $name?? '' }}" type="checkbox">
        </div>
        <div class="ml-3 text-sm/6">
            <label for="{{ $name ?? '' }}">{{ $label ?? '' }}
        </div>
    </div>
</div>