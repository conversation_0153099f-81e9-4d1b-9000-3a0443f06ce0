@props(
    [
        'id' => null,
        'name' => null,
        'type' => 'text',
        'placeholder' => null,
        'icon' => null,
        'label' => null,
    ]
)
<div x-data="Input" data-module="ds-input" {{ $attributes->merge(['class' => 'ds-input']) }}>
    <label for="{{ $name ?? '' }}">{{ $label ?? '' }}
    <div class="ds-input__container">
        <input type="{{ $type ?? '' }}" name="{{ $name ?? '' }}" id="{{ $id ?? '' }}" placeholder="{{ $placeholder ?? '' }}" {{ $attributes }}>
        @if(isset($icon))
            <div class="ds-input__icon-container">
                <div class="text-stone-400">
                    <i class="fa-light fa-{{ $icon ?? '' }}"></i>
                </div>
            </div>
        @endif
    </div>
</div>