@props([
	'type' => 'primary',
	'href' => null,
    'icon_prepend' => null,
	'alpineHref' => null,
])

<{{ $href !=null || $alpineHref != null ? 'a href=' .$href : 'button' }} data-module="ds-button" {{ $attributes->merge(['class' => 'ds-button-2 group ds-button-2--'.$type]) }}>
    <div class="ds-button-2__prepend">
        <div class="ds-button-2__icon-container">
            <i class="{{ $icon_prepend }}"></i>
        </div>
        <p class="ds-button-2__text !leading-none">{{ $slot }}</p>
    </div>
    <div class="ds-button-2__append">
        <i class="fa-sharp fa-regular fa-arrow-right-long"></i>
    </div>
<{{ $href !=null || $alpineHref != null ? '/a' : '/button' }}>