@props([])

<div x-data="TeamMember" data-module="ds-team-member" {{ $attributes->merge(['class' => 'ds-team-member']) }}>
    <div x-on:keydown.window.escape="teamoverlay = true" x-show="teamoverlay" x-cloak
        class="fixed inset-0 z-50 h-screen overflow-hidden" aria-labelledby="slide-over-title" x-ref="dialog"
        aria-modal="true">
        <div class="absolute inset-0 overflow-hidden">
            <div x-transition:enter="ease-in-out duration-500" x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100" x-transition:leave="ease-in-out duration-500"
                x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                x-description="Background overlay, show/hide based on slide-over state."
                class="absolute inset-0 transition-opacity bg-[#020519] bg-opacity-70" x-on:click="disableBodyLock(), teamoverlay = false"
                aria-hidden="true">
            </div>
            <div class="absolute inset-y-0 right-0 flex max-w-full md:pl-10">
                <div x-show="teamoverlay"
                    x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
                    x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0"
                    x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
                    x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full"
                    class="w-screen max-w-full md:max-w-3xl"
                    x-description="Slide-over panel, show/hide based on slide-over state.">
                    <div class="relative flex flex-col h-full pt-4 pb-10 overflow-y-scroll shadow-xl overscroll-contain bg-neutral-600 md:pb-16">
						<button x-on:click="disableBodyLock(), teamoverlay = false" class="absolute text-4xl duration-200 top-6 right-6 dark hover:text-neutral-300">
							<i class="fa-sharp fa-light fa-xmark"></i>
						</button>
                        <div class="relative flex-1 mt-16 md:mt-24">
                            <div class="flex flex-col px-4 md:px-28 dark">
                                <div class="flex-shrink-0 max-w-lg overflow-hidden rounded-lg aspect-square">
                                    <img :src="profile_image" class="object-cover">
                                </div>
								<div class="flex justify-between max-w-lg mt-14">
									<div>
                                		<h3 class="mb-2 text-3xl font-medium" x-text="name"></h3>
										<p class="font-medium text-neutral-400" x-text="position"></p>
									</div>
									<div x-show="linkedin_profile != ''" x-cloak class="flex-shrink-0">
										<a :href="linkedin_profile" target="_blank" class="flex items-center justify-center hover:bg-white/20 duration-200 h-[44px] w-[44px] rounded-full border border-white">
											<i class="fa-brands fa-linkedin-in" aria-hidden="true"></i>
										</a>
									</div>
								</div>
								<ul role="list" class="mt-6 space-y-4 sm:flex sm:space-x-4 sm:space-y-0">
									<li>
										<x-button ::href="'mailto:'+email" type="secondary" alpineHref icon_prepend="fa-solid fa-envelope">
                                            Email <span x-text="name != null ? name.split(' ')[0] : ''"></span>
                                        </x-button>
									</li>
									<li x-show="phone" x-cloak>
										<x-button ::href="'tel:'+phone" type="secondary" alpineHref icon_prepend="fa-solid fa-phone"> 
                                            <span x-text="phone"></span>
                                        </x-button>
									</li>
								</ul>
								<div class="mt-4 text-block" x-html="biography">
								</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
