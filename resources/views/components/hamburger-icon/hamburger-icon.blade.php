@props([])

<div x-data="HamburgerIcon" data-module="ds-hamburger-icon" {{ $attributes->merge(['class' => 'ds-hamburger-icon']) }}>
    <button class="z-[51] relative w-[29px] lg:w-[38px] h-[14px] lg:h-[17px] focus:outline-none group" x-on:click="menuopen = !menuopen">
      <span class="sr-only">Open main menu</span>
         <div class="absolute block w-[29px] lg:w-[38px] transform -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2">
            <span aria-hidden="true"
                  class="hamburger-line block absolute h-[2px] w-[29px] lg:w-[38px]  transform transition duration-300 ease-in-out"
                  :class="{'rotate-45 group-hover:text-neutral-300': menuopen,'-translate-y-1.5 group-hover:-translate-y-2  bg-white': !menuopen }">
            </span>
            <span aria-hidden="true"
                  class="hamburger-line block absolute h-[2px] w-[29px] lg:w-[38px] transform transition duration-300 ease-in-out"
                  :class="{'-rotate-45 group-hover:text-neutral-300': menuopen, 'translate-y-1.5 group-hover:translate-y-2  bg-white': !menuopen}">
            </span>
         </div>
    </button>
</div>