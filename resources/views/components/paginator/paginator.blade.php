@props(
    [
        'paginator' => [],
        'type' => 'simple',
    ]
)
<div x-data="Paginator" data-module="ds-paginator" {{ $attributes->merge(['class' => 'ds-paginator ds-paginator--'.$type]) }}>
    
    @if ($paginator)

        @if($type === 'simple')

            <div class="pagination">
                @if ($paginator['prev_page'])
                    <x-button href="{{ $paginator['prev_page'] }}">
                        <i class="mr-1 fa-light fa-chevron-left"></i>
                         Previous
                    </x-button>
                @else
                    <x-button class="opacity-50 !cursor-not-allowed">
                        <i class="mr-1 fa-light fa-chevron-left"></i>
                        Previous
                    </x-button>
                @endif

                <span>{{ $paginator['current_page'] }} of {{ $paginator['total_pages'] }} pages</span>

                @if ($paginator['next_page'])
                    <x-button href="{{ $paginator['next_page'] }}">
                        Next
                        <i class="ml-1 fa-light fa-chevron-right"></i>
                    </x-button>
                @else
                    <x-button class="opacity-50 !cursor-not-allowed">
                        Next
                        <i class="ml-1 fa-light fa-chevron-right"></i>
                    </x-button>
                @endif
            </div>

        @elseif($type === 'auto')

            {!! $paginator['auto_links'] !!}
    
        @endif

    @endif
    
</div>