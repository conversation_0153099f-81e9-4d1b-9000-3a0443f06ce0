@props([
'type' => 'download-1',
'icon' => null,
'title' => '',
'file' => null,
])

<a href="{{ $file->url }}" target="_blank" data-module="ds-download" {{ $attributes->merge(['class' => 'ds-download ds-download--custom group
    ds-download--'.$type]) }}>
    <div class="flex items-center">
        <div class="ds-download__icon-container">
            <div class="ds-download__icon">
                @if($file->extension == 'pdf')
                <i class="fa-regular fa-file-pdf"></i>
                @elseif($file->extension == 'doc' || $file->extension == 'docx')
                <i class="fa-regular fa-file-word"></i>
                @elseif($file->extension == 'xls' || $file->extension == 'xlsx')
                <i class="fa-regular fa-file-excel"></i>
                @elseif($file->extension == 'ppt' || $file->extension == 'pptx')
                <i class="fa-regular fa-file-powerpoint"></i>
                @elseif($file->extension == 'zip')
                <i class="fa-regular fa-file-zip"></i>
                @elseif($file->extension == 'jpg' || $file->extension == 'jpeg' || $file->extension == 'png' ||
                $file->extension == 'gif')
                <i class="fa-regular fa-file-image"></i>
                @else
                <i class="fa-regular fa-file"></i>
                @endif
            </div>
        </div>
        <h3 class="!mb-0">{{ $file->alt ?? $file->title }}</h3>
    </div>
    <i class="fa-light fa-arrow-down-to-line"></i>
</a>