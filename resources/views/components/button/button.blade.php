@props([
    'type' => 'primary',
    'icon_prepend' => null,
    'icon_append' => null,
    'image_append' => null,
    'href' => null,
    'alpineHref' => null,
    'target' => null, // Specify the Livewire target (e.g., a method or property)
    'window_target' => '_self'
])

<{{ $href != null || $alpineHref != null ? 'a href=' . $href : 'button' }} 
    data-module="ds-button" 
    {{ $attributes->merge(['class' => 'ds-button group ds-button--' . $type]) }}
    {{ $href != null || $alpineHref != null ? 'target=' . $window_target : '' }}
    wire:loading.attr="disabled"
>
    @isset($icon_prepend)
        <div class="ds-button__icon-container">
            <span class="relative z-10">
                <i class="{{ $icon_prepend }}"></i>
            </span>
        </div>
    @endisset
    @isset($image_append)
        <img src="{{ $image_append }}" class="w-24 mr-2 -mt-0.5 ds-button__image-append" alt="">
    @endisset

    <span class="relative z-10" wire:loading.remove {{ $target ? "wire:target={$target}" : '' }}>
        {{ $slot }}
    </span>

	@if($target !== null)
    <span class="relative z-10 flex items-center space-x-2" wire:loading {{ $target ? "wire:target={$target}" : '' }}>
        <i class="fas fa-spinner fa-spin"></i>
        <span>Please wait...</span>
    </span>
	@endif

    @isset($icon_append)
        <div class="ds-button__icon-container">
            <span class="relative z-10">
                <i class="{{ $icon_append }}"></i>
            </span>
        </div>
    @endisset
<{{ $href != null || $alpineHref != null ? '/a' : '/button' }}>
