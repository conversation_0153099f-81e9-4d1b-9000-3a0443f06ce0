@props([
   'stat' => 100, 
   'title' => '',
   'citation' => '',
   'decimal' => str_contains($stat, '.'),
   'prepend' => '',
   'append' => ''
])

<div x-data="Stat" data-module="ds-stat" {{ $attributes->merge(['class' => 'ds-stat']) }}>
   <dt>
      {{ $title ?? '' }}
      <p>({{ $citation ?? '' }})</p>
   </dt>
   <img src="/img/red-underline.svg" alt="red underline" class="py-3">
   @if($decimal)
   <dd x-data="animatedCounter({{ $stat }}, 300, 0)" class="not-prose" x-intersect="updatecounter" x-text="'{{ $prepend ?? '' }}' + current.toFixed(1) + '{{ $append ?? '' }}'"></dd>
   @else
   <dd x-data="animatedCounter({{ $stat }}, 300, 0)" class="not-prose" x-intersect="updatecounter" x-text="'{{ $prepend ?? '' }}' + Math.round(current) + '{{ $append ?? '' }}'"></dd>
   @endif 
</div>

<script>
    function animatedCounter(target, time = 300, start = 0) {
        return {
        current: 0,
        target: target,
        time: time,
        start: start,
        updatecounter: function() {
            start = this.start;
            const increment = (this.target - start) / this.time;
            const handle = setInterval(() => {
            if (this.current < this.target)
               this.current += increment
            else {
               clearInterval(handle);
               this.current = this.target
            }
            }, 1);
        }
        };
   }
</script>