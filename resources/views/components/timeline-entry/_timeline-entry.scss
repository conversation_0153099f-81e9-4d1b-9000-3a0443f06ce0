.ds-timeline-entry {
    @apply flex md:contents;
    &__timeline-container {
        @apply flex relative col-start-1 mr-4 lg:mr-6 md:mx-auto;
    }
    &__timeline-container-inner {
        @apply flex items-start justify-center w-10 h-full;
    }
    &__timeline {
        @apply h-full w-1 bg-white pointer-events-none;
    }
    &__icon-container {
        @apply absolute top-0 w-10 h-10 flex items-center justify-center bg-gradient-to-tr from-[#B90226] to-red-600 rounded-md;
    }
    &__icon {
        @apply text-lg text-white text-center;
    }
    &__entry-container {
        @apply col-start-2 col-end-12 mb-12 lg:mb-20 lg:pl-5 xl:pl-2;
    }
    &__entry-title {
        @apply mb-2 text-xl lg:text-2xl font-medium;
    }
    &__badge {
        @apply mb-2;
    }
}