@props([
    'icon' => 'fas fa-rocket',
    'badge' => null,
    'title' => null,
    'description' => null,
])
<div x-data="TimelineEntry" data-module="ds-timeline-entry" {{ $attributes->merge(['class' => 'ds-timeline-entry']) }}>
    <!-- Actual timeline -->
    <div class="ds-timeline-entry__timeline-container">
        <div class="ds-timeline-entry__timeline-container-inner">
            <div class="ds-timeline-entry__timeline"></div>
        </div>
        <div class="ds-timeline-entry__icon-container" data-aos="fade-up" data-aos-duration="1000">
            <div class="ds-timeline-entry__icon">
                <i class="{{ $icon }}"></i>
            </div>
        </div>
    </div>
    <!-- Entry contents -->
    <div class="ds-timeline-entry__entry-container dark" data-aos="fade-up" data-aos-duration="1000">
        @isset($badge)
            <x-badge type="secondary" class="ds-timeline-entry__badge">
                {{ $badge ?? '' }}
            </x-badge>
        @endisset
        <h2 class="ds-timeline-entry__entry-title">{{ $title  ?? '' }}</h2>
        {!! $description  ?? '' !!}
    </div>
</div>
