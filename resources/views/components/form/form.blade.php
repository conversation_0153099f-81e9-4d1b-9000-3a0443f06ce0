@props([
	'handle' => null,
	'autoGenerate' => true,
	'successMessage' => 'Submission successfully received.',
])

<div data-module="ds-form" {{ $attributes->merge(['class' => 'ds-form']) }}>

	@if(!$autoGenerate)
	{{-- @include('partials.forms.maintenance-request') --}}
	@else

	{!! $slot !!}

	<div class="px-6 py-6 space-y-4 border rounded-lg shadow custom-form md:space-y-6 border-neutral-200 md:rounded-xl">

		@php
		$handle = 'maintenance-request';
		$form = \Statamic\Facades\Form::find($handle);
		$data = [];
		foreach ($form->blueprint()->tabs() as $tab){
			array_push($data, $tab->contents()['sections']);
		}
		ray($data)->red();
		@endphp

	<form method="POST" action="{{ route('statamic.forms.submit', ['form' => $handle]) }}" enctype="multipart/form-data" redirect="/our-services/maintenance-request/thanks">
			@csrf
			@method('POST')
			
			@if (isset(session('form')[$handle]))
				
				<div class="px-4 py-3 mb-4 text-green-700 bg-green-100 border border-green-400 rounded">
					{{ $successMessage }}
				</div>
				
			@else
				
				@if(session('errors'))
					<ul class="px-4 py-3 mb-4 text-red-700 bg-red-100 border border-red-400 rounded">
						@foreach(session('errors')->getBag('form.maintenance-request')->all() as $error)
							<li>{{ $error }}</li>
						@endforeach
					</ul>
				@endif
				
				@foreach ($data[0] as $section)
				<section class="pb-8">
						<div class="px-4 py-2 rounded bg-emerald-950">
							<h2 class="text-lg leading-6 -mb-0.5 text-white">{{ $section['display'] }}</h2>
						</div>
						@isset($section['instructions'])
						<p class="pt-4 form-instructions">
							{{ $section['instructions'] }}
						</p>
						@endisset
						{{-- @ray($field) --}}
						<div class="grid grid-cols-4 gap-4 mt-4">
							@foreach ($section['fields'] as $field)
							@php
							$fieldConfig = $field['field'];
							switch ($fieldConfig['width'] ?? 100) {
							case '25':
							$width = 'md:col-span-1';
							break;
							case '50':
							$width = 'md:col-span-2';
							break;
							case '75':
							$width = 'md:col-span-3';
							break;
							case '100':
							default:
							$width = 'md:col-span-4';
							break;
							}
							@endphp

							<div class="col-span-4 {{ $width }}">
								<label for="{{ $field['handle'] }}" class="block text-sm font-medium text-gray-700">
									{{ $fieldConfig['display'] }}
								</label>
								@if ($fieldConfig['type'] === 'text')
								<input type="text" name="{{ $field['handle'] }}" id="{{ $field['handle'] }}"
									class="block w-full mt-1 border border-gray-300 rounded-md shadow-sm">
								@elseif ($fieldConfig['type'] === 'textarea')
								<textarea name="{{ $field['handle'] }}" id="{{ $field['handle'] }}"
									class="block w-full mt-1 border border-gray-300 rounded-md shadow-sm"></textarea>
								@elseif ($fieldConfig['type'] === 'select')
								<select name="{{ $field['handle'] }}" id="{{ $field['handle'] }}"
									class="block w-full mt-1 border border-gray-300 rounded-md shadow-sm">
									@foreach ($fieldConfig['options'] as $option)
									<option value="{{ $option }}">{{ $option }}</option>
									@endforeach
								</select>
								@elseif ($fieldConfig['type'] === 'assets')
								@include('vendor.statamic.forms.fields.assets', ['handle' => $field['handle'], 'field' => $fieldConfig])
								@endif
							</div>
							@endforeach
						</div>
				</section>
				@endforeach
				<x-button type="submit" class="ds-button--primary__icon" icon_append="fa-solid fa-angle-right">
					Submit
				</x-button>
			@endif
		</form>
	</div>
	@endif
</div>