@foreach($content as $block)

    {{-- component/accordion --}}
    @if($block->type == "accordion")
    <div class="mx-auto max-w-screen-2xl" data-aos="fade-up" data-aos-duration="1000">
        @foreach($block->items as $item)
            <x-accordion :title="$item->title" :icon="$item->icon">
                {!! $item->content !!}
            </x-accordion>
        @endforeach
    </div>
    
    {{-- component/announcement --}}
    @elseif($block->type == "announcement")
    <div class="mx-auto max-w-screen-2xl" data-aos="fade-up" data-aos-duration="1000">
        <x-announcement :title="$block->title">
            {!! $block->content !!}
        </x-announcement>
    </div>

    {{-- component/button --}}
    @elseif($block->type == "button")
    <div class="mx-auto max-w-screen-2xl" data-aos="fade-up" data-aos-duration="1000">
        <x-button type="primary" class="mt-8" :href="$block->url" :icon_append="$block->icon">
            {{ $block->label }}
        </x-button>
    </div>
        
    {{-- component/fifty-fifty --}}
    @elseif($block->type == "fifty-fifty")
    <x-fifty-fifty data-aos="fade-up" data-aos-duration="1000">
        <x-slot name="slot1">
            <x-image src="{{ $block->image }}" glideWidth="800" alt="" />
        </x-slot>
        <x-slot name="slot2">
            <div class="max-w-full text-block">
                {!! $block->content !!}
            </div>
        </x-slot>
    </x-fifty-fifty>

    {{-- custom_code --}}
    @elseif($block->type == "custom_code")
    {!! $block->custom_code !!}

    {{-- component/downloads --}}
    @elseif($block->type == "downloads")
    <div class="w-screen ml-[-50vw] left-1/2 ds-site-padding ds-section-padding bg-neutral-100" data-aos="fade-up" data-aos-duration="1000">
        <div class="max-w-screen-md mx-auto space-y-4">
         <h2>Downloads</h2>
            @foreach($block->files as $file)
                <x-download :file="$file" />
            @endforeach
        </div>
    </div>

    {{-- component/feature --}}
    @elseif($block->type == "features_grid")
    <div class="w-screen ml-[-50vw] left-1/2 ds-site-padding ds-section-padding bg-[#E6F1F0] {{ $block->fg_container_classes }}" data-aos="fade-up" data-aos-duration="1000">
        <div class="grid max-w-screen-md mx-auto md:grid-cols-{{ $block->number_of_columns }} gap-x-16 gap-y-8 md:gap-y-12">
            @if($block->title)
                <h2 class="md:col-span-{{ $block->number_of_columns }} text-3xl">{{ $block->title }}</h2>
            @endif
            @foreach($block->features as $feature)
                <x-feature :icon="$feature->icon" :title="$feature->feature_title">
                    {!! $feature->content !!}
                </x-feature>
            @endforeach
        </div>
    </div>

    {{-- form --}}
    @elseif($block->type == "form")
    <x-form :handle="$block->form->handle" :autoGenerate="$block->auto_generate_form" :successMessage="$block->success_message" />

    {{-- image --}}
    @elseif($block->type == "image")
    <section class="ds-image entry-image align-{{ $block->alignment }} size-{{ $block->size }}">
        <img src="{{ $block->image }}" alt="{{ $block->image->alt }}" {!! $block->size=='custom' ? 'style="width:'.$block->width.'px; height:'.$block->height.'px"' : '' !!} />
    </section>

    {{-- image_group --}}
    @elseif($block->type == "image_group")
    <div class="grid grid-cols-2 gap-6 mt-8 md:gap-10 mb-14 md:mt-12 md:mb-16">
        @foreach($block->group_images as $image)
            <img src="{{ $image['url'] }}" alt="{{ $image['caption'] }}"/>
        @endforeach
    </div>

    {{-- component/list --}}
    @elseif($block->type == "list")
    <div class="max-w-screen-md mx-auto text-block" data-aos="fade-up" data-aos-duration="1000">
        @if($block->title)
        <h2>{{ $block->title }}</h2>
        @endif
        <x-list :icon="$block->bullet_point_icon" :font_icon="$block->font_icon" :items="$block->list" class="mb-0" />
    </div>

    {{-- partial --}}
    @elseif($block->type == "partial")
    @include("$block->partial", ["page" => $page])

    {{-- component/stat --}}
    @elseif($block->type == "stat")
    <div class="max-w-screen-lg py-8 mx-auto border-y md:py-14"  data-aos="fade-up" data-aos-duration="1000">
        <div class="grid grid-cols-2 md:grid-cols-3 gap-y-12 md:divide-x">
            @foreach($block->stat as $stat)
                <x-stat :stat="$stat->number" :title="$stat->label" :prepend="$stat->prepend" :append="$stat->append" />
            @endforeach
        </div>
    </div>
    
    {{-- component/text --}}
    @elseif($block->type == "text")
    @empty($inline)
        <div class="text-block" data-aos="fade-up" data-aos-duration="1000">
            @endempty
            @include("partials/sets/".$block['type'], ['content' => $block])
            @empty($inline)
        </div>
    @endempty

    {{-- component/video --}}
    @elseif($block->type == "video")
    <section class="ds-site-padding ds-section-padding">
        <div class="max-w-screen-md mx-auto">
            <div class="text-block" data-aos="fade-up" data-aos-duration="1000">
                @include("partials/sets/".$block['type'], ['content' => $block])
            </div>
        </div>
    </section>

    @else
    {{ $block->type }}
    <!-- dynamic component based on $block->type -->
    <x-dynamic-component :component="$block->type" :content="$block" />
        
    @endif

@endforeach
