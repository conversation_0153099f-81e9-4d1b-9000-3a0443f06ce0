#design-system {
    font-family: system-ui;
    @apply flex min-h-screen bg-white;

    main {
        @apply w-full p-5;
    }

    h1 {
        @apply mb-2 text-3xl font-semibold tracking-normal;
    }
    p {
        @apply mb-2 text-gray-600;
    }
    .btn-primary {
        @apply px-3 py-2 text-sm font-semibold text-white rounded-md shadow-sm bg-sky-500 hover:bg-sky-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600;
    }
    .btn-danger {
        @apply px-3 py-2 text-sm font-semibold text-white bg-red-500 rounded-md shadow-sm hover:bg-red-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600;
    }
    .btn-edit-code {
        @apply absolute z-50 px-3 py-1 !text-sm text-white rounded top-4 right-4 bg-white/10 hover:bg-white/20;
    }
    .btn-lg {
        @apply px-4 py-2 text-lg font-light;
    }
    @apply flex grow-0;

    #ds-nav {
        @apply flex flex-col text-gray-600 hover:text-gray-900;
        min-width: 250px;
        font-size: 0.95rem;
        svg {
            width: 14px;
        }
        ul {
            @apply space-y-1;
            li {
                @apply text-gray-600;
                div {
                    @apply text-gray-600;
                }
                a {
                    @apply flex justify-between text-gray-600;
                    svg {
                        @apply hidden;
                    }
                }
                &.active {
                    @apply font-medium;
                    a {
                        @apply !text-sky-500;
                        svg {
                            display: block;
                        }
                    }
                }
                div.active, div.active div {
                    @apply font-medium !text-sky-500;
                }
            }  
        }
    }

    .component-status {
        &--draft {
            @apply fill-yellow-500;
        }
        &--wip {
            @apply fill-orange-400;
        }
        &--published {
            @apply fill-green-500;
        }
        &--depreciated {
            @apply fill-red-500;
        }
    }
    .ds-device {
        @apply flex flex-col justify-center border rounded-xl bg-gray-50;
        &__bar {
            @apply flex items-center justify-between w-full h-10 px-6 space-x-2 bg-gray-100 rounded-t-xl;
        }
        &__bar-buttons {
            @apply flex space-x-2;
        }
        &__bar-button {
            @apply w-3.5 h-3.5 rounded-full;
        }
        &__bar-devices {
            @apply flex space-x-1;
        }
        &__bar-device {
            @apply px-2 py-1;
            svg {
                @apply w-5 h-5 cursor-pointer fill-gray-400;
            }
            &.active {
                @apply bg-white border rounded;
                svg {
                    @apply fill-sky-500;
                }
            }
        }
        &__content {
            @apply overflow-x-hidden bg-white border-x;
            &[data-device="desktop"] {
                @apply w-full bg-white border-none rounded-b-xl;
            }
            @apply p-5 mx-auto;
        }
    }
    .ds-code-block {
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow: auto;
        @apply relative h-full px-5 py-1 text-sm bg-gray-800 rounded-md;
         .line-number {
            margin-right: 10px;
         }
    }

    #view-component {
        .ds-icon svg {
            @apply w-10 h-10 fill-gray-500;
        }
    }

    #component-config {
        
    }
    .component-settings {
        form {
            @apply mx-2 my-2;
        }
    }

    .instructions-text {
        @apply pl-2 text-sm;
        p {
            @apply mb-1;
        }
    }

    /* Design System components (i.e. those used in the DS interface, not actual components for the site */
    .alert {
        @apply flex p-3 mb-5 text-sm rounded-md;
        &.alert-info {
            @apply bg-sky-50 text-sky-900;
            p {
                @apply text-sky-900;
            }
            i {
                @apply !text-sky-500;
            }
        }
        &.alert-success {
            @apply text-green-900 bg-green-50;
            p {
                @apply text-green-900;
            }
            i {
                @apply text-green-500;
            }
        }
        &.alert-warning {
            @apply text-yellow-900 bg-yellow-50;
            p {
                @apply text-yellow-900;
            }
            i {
                @apply text-yellow-500;
            }
        }
        &.alert-danger {
            @apply text-red-900 bg-red-50;
            p {
                @apply text-red-900;
            }
            i {
                @apply text-red-500;
            }
        }
    }

    .used-in {
        @apply flex px-5 py-3 mb-5 text-sm text-gray-900 rounded-md bg-gray-50;
        p {
            @apply text-gray-900;
        }
        i {
            @apply text-gray-500;
        }
    }
    
}