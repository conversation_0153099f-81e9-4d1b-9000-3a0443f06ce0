<div class="flex justify-between px-6 py-10 space-x-20">

    <div>
        <h1 class="!text-5xl !font-bold text-sky-300">
            UX/UI
        </h1>
        <h2 class="my-2 text-6xl text-sky-700 !font-thin tracking-normal">
            Design System
        </h2>

        <div class="mt-8 text-lg tracking-normal">
            <p class="!text-2xl !mb-5 tracking-normal">
                Welcome to the {{ env('APP_NAME') }} design system.
            </p>
            <p>
                The Design System helps you build reusable UI components in isolation from your app's business logic, data, and context.
            </p>
            
            <div class="grid grid-cols-1 gap-1">

                <a href="{{ config('designsystem.navigation') !== '' ? '/admin/navigation/'.config('designsystem.navigation') : '/admin/collections/pages' }}" target="_blank" class="flex items-center justify-center mt-5 btn-primary btn-lg">
                    <i class="mr-3 fa-regular fa-list-tree"></i>
                    Manage navigation <span class="pl-2 opacity-70">[{{ config('designsystem.navigation') !== '' ? config('designsystem.navigation') : 'pages' }}]</span>
                </a>

                <button x-on:click="$wire.viewNewPageConfig" type="button" class="mt-5 btn-primary btn-lg">
                    <i class="mr-1 fa-regular fa-plus"></i>
                    Create new page
                </button>

                <button x-on:click="$wire.viewNewComponentConfig" type="button" class="mt-5 btn-primary btn-lg">
                    <i class="mr-1 fa-regular fa-plus"></i>
                    Create new component
                </button>

            </div>
        </div>
    </div>

    <img src="/img/ux.svg" alt="" class="w-1/2" />

</div>