<form wire:submit="pageConfig" class="mx-5 my-10 space-y-4">

    @if($page['is_new'])
    <div class="flex items-center mt-4 mb-6">
        <div class="mr-2 ds-icon">
            <i class="mr-1 text-3xl fa-duotone fa-file-plus"></i>
        </div>
        <h2 class="flex text-2xl font-light tracking-normal text-gray-800">
            New page
        </h2>
    </div>
    @endif
    
    <div class="w-full form-group publish-field publish-field__title text-fieldtype">
        <div class="field-inner">
            <label for="field_title" class="block text-sm font-medium leading-6 text-gray-900">
                <span class="rtl:ml-1 ltr:mr-1 v-popper--has-tooltip">Name</span>
                <i class="text-red-500 required rtl:ml-1 ltr:mr-1">*</i>
            </label>
        </div>
        <div class="flex items-center">
            <div class="input-group">
                <input wire:model="page.name" type="text" autofocus="autofocus" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6">
                <div>@error('page.name') {{ $message }} @enderror</div>
            </div>
        </div>
    </div>

    {{-- <div class="w-full form-group publish-field publish-field__title text-fieldtype">
        <div class="field-inner">
            <label for="field_title" class="block text-sm font-medium leading-6 text-gray-900">
                <span class="rtl:ml-1 ltr:mr-1 v-popper--has-tooltip">Group</span>
                <i class="text-red-500 required rtl:ml-1 ltr:mr-1">*</i>
            </label>
        </div>
        <div class="flex items-center">
            <div class="input-group">
                <select wire:model.live="component.group" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6" {{ $page['is_new'] ? '' : 'disabled' }}>
                    <option value="null">No group</option>
                    @foreach($navGroups as $group)
                    <option value="{{ $group }}">{{ $group }}</option>
                    @endforeach
                    <option value="Enter group name">Create new group</option>
                </select>
                @if(!$page['is_new'])
                <p class="pt-1 text-xs italic !text-gray-400">Any changes to the group must be carried out manually</p>
                @endif
            </div>
        </div>
    </div> --}}
    {{-- <ul>
        @foreach (Statamic::tag('nav')->handle('main_nav')->fetch() as $navItem)
            <li>
                <a href="{{ $navItem['url'] }}">{{ $navItem['title'] }}</a>
                @if (!empty($navItem['children']))
                    <ul class="pl-4">
                        @foreach ($navItem['children'] as $child)
                            <li>
                                <a href="{{ $child['url'] }}">{{ $child['title'] }}</a>
                            </li>
                        @endforeach
                    </ul>
                @endif
            </li>
        @endforeach
    </ul> --}}


    <div class="w-full form-group publish-field publish-field__title text-fieldtype">
        <div class="field-inner">
            <label for="field_title" class="block text-sm font-medium leading-6 text-gray-900">
                <span class="rtl:ml-1 ltr:mr-1 v-popper--has-tooltip">Status</span>
                <i class="text-red-500 required rtl:ml-1 ltr:mr-1">*</i>
            </label>
        </div>
        <div class="flex items-center">
            <div class="input-group">
                <select wire:model="page.status" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6">
                    <option value="draft">Draft</option>
                    <option value="wip">WIP</option>
                    <option value="published">Published</option>
                    <option value="depreciated">Depreciated</option>
                </select>
                <!-- TODO: format error messages -->
                <div>@error('page.status') {{ $message }} @enderror</div>
            </div>
        </div>
    </div>

    <div class="flex justify-between">
        <button type="submit" class="btn-primary">
            {{ $page['is_new'] ? 'Create new page' : 'Update page' }}
        </button>
        @if(!$page['is_new'])
        <button x-on:click="if (confirm('Are you sure you want to delete this page?')) { $wire.deletePage('{{ $slug }}') }" type="button" class="btn-danger">
            Delete
        </button>
        @endif
    </div>
</form>