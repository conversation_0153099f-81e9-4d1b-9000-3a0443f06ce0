<form wire:submit="componentConfig" class="mx-5 my-10 space-y-4">

    @if($component['is_new'])
    <div class="flex items-center mt-4 mb-6">
        <div class="mr-2 ds-icon">
            <i class="mr-1 text-3xl fa-duotone fa-cube"></i>
        </div>
        <h2 class="flex text-2xl font-light tracking-normal text-gray-800">
            New component
        </h2>
    </div>
    @endif
    
    <div class="w-full form-group publish-field publish-field__title text-fieldtype">
        <div class="field-inner">
            <label for="field_title" class="block text-sm font-medium leading-6 text-gray-900">
                <span class="rtl:ml-1 ltr:mr-1 v-popper--has-tooltip">Name</span>
                <i class="text-red-500 required rtl:ml-1 ltr:mr-1">*</i>
            </label>
        </div>
        <div class="flex items-center">
            <div class="input-group">
                <input wire:model="component.name" type="text" autofocus="autofocus" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6">
                <div>@error('component.name') {{ $message }} @enderror</div>
            </div>
        </div>
    </div>

    <div class="w-full form-group publish-field publish-field__title text-fieldtype">
        <div class="field-inner">
            <label for="field_title" class="block text-sm font-medium leading-6 text-gray-900">
                <span class="rtl:ml-1 ltr:mr-1 v-popper--has-tooltip">Group</span>
                <i class="text-red-500 required rtl:ml-1 ltr:mr-1">*</i>
            </label>
        </div>
        <div class="flex items-center">
            <div class="input-group">
                <select wire:model.live="component.group" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6" {{ $component['is_new'] ? '' : 'disabled' }}>
                    <option value="null">No group</option>
                    @foreach($navGroups as $group)
                    <option value="{{ $group }}">{{ $group }}</option>
                    @endforeach
                    <option value="Enter group name">Create new group</option>
                </select>
                @if(!$component['is_new'])
                <p class="pt-1 text-xs italic !text-gray-400">Any changes to the group must be carried out manually</p>
                @endif
                @if($component['is_new'] && $component['group']=='Enter group name')
                <input wire:model="component.group" type="text" placeholder="i.e. Carousel"  autofocus="autofocus" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6 mt-2">
                @endif
            </div>
        </div>
    </div>

    <div class="w-full form-group publish-field publish-field__title text-fieldtype">
        <div class="field-inner">
            <label for="field_title" class="block text-sm font-medium leading-6 text-gray-900">
                <span class="rtl:ml-1 ltr:mr-1 v-popper--has-tooltip">Status</span>
                <i class="text-red-500 required rtl:ml-1 ltr:mr-1">*</i>
            </label>
        </div>
        <div class="flex items-center">
            <div class="input-group">
                <select wire:model="component.status" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6">
                    <option value="draft">Draft</option>
                    <option value="wip">WIP</option>
                    <option value="published">Published</option>
                    <option value="depreciated">Depreciated</option>
                </select>
                <div>@error('component.status') {{ $message }} @enderror</div>
            </div>
        </div>
    </div>

    <div class="w-full form-group publish-field publish-field__title text-fieldtype">
        <div class="field-inner">
            <label for="field_title" class="block text-sm font-medium leading-6 text-gray-900">
                <span class="rtl:ml-1 ltr:mr-1 v-popper--has-tooltip">Icon</span>
                <i class="text-red-500 required rtl:ml-1 ltr:mr-1">*</i>
            </label>
            <div class="py-0 text-xs text-gray-300">
                <p>Enter <a href="https://fontawesome.com/search" target="_blank" class="text-sky-500">Font Awesome</a> classes or alternatively enter an icon svg</p>
            </div>
        </div>
        <div class="flex items-center">
            <div class="w-full input-group">
                <textarea wire:model="component.icon" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"></textarea>
                <div>@error('component.icon') {{ $message }} @enderror</div>
            </div>
        </div>
    </div>

    <div class="flex justify-between">
        <button type="submit" class="btn-primary">
            {{ $component['is_new'] ? 'Save new component' : 'Update component' }}
        </button>
        @if(!$component['is_new'])
        <button x-on:click="if (confirm('Are you sure you want to delete this component?')) { $wire.deleteComponent('{{ $slug }}', '{{ $component['group'] ?? '' }}') }" type="button" class="btn-danger">
            Delete
        </button>
        @endif
    </div>
</form>