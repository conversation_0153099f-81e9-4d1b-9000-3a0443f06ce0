<nav id="ds-nav" class="flex flex-col p-5 mr-5 bg-gray-100 border-r nav-main min-w-48">
    <a href="{{ env('APP_URL') }}" target="_blank">
        <img src="{{ asset(config('designsystem.logo')) }}" alt="Logo" class="w-full mb-5" />
    </a>
    <a href="/admin" class="flex mb-2 cursor-pointer">
        <i><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width:18px; margin-top:2px; margin-right: 7px"><circle cx="7" cy="8.5" r="3.5" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></circle><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M7 5v3.5h3.5M9 22.5a6.979 6.979 0 0 0 1.5-4m4.5 4a6.979 6.979 0 0 1-1.5-4m-6.001 4h9M.5 15.5h23"></path><rect width="23" height="17" x=".5" y="1.5" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" rx="1" ry="1"></rect><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M13.5 7 15 5l3 2.5 2.5-3m-1 8V11m-2 1.5v-2m-2 2v-3m-2 3V11"></path></svg></i>
        Statamic dashboard
    </a>
    <a wire:click="loadComponent(null)" x-on:click="$dispatch('close-sub-menu')" class="mb-2 cursor-pointer flex items-center {{ $component['name']=='' && $page['name']=='' ? 'text-sky-500 font-medium' : '' }}">
        <i><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width:18px; margin-top:2px; margin-right: 7px"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M18 2.781a1 1 0 0 0-.713-.958l-4-1.2a1 1 0 0 0-.575 0l-4 1.2A1 1 0 0 0 8 2.781V7.86a1 1 0 0 0 .629.928l4 1.6a1 1 0 0 0 .743 0l4-1.6A1 1 0 0 0 18 7.86V2.781z"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M17.745 2.114 13 3.537 8.255 2.114M13 3.5v7m10 2.384a.5.5 0 0 0-.324-.468l-3.5-1.312a.5.5 0 0 0-.351 0l-3.5 1.312a.5.5 0 0 0-.324.468v3.844a.5.5 0 0 0 .276.447l3.5 1.75a.5.5 0 0 0 .447 0l3.5-1.75a.5.5 0 0 0 .276-.447v-3.844z"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M22.894 12.577 19 14.037l-3.893-1.46M19 14v5m-6-4.742a1 1 0 0 0-.684-.949l-5-1.667a1 1 0 0 0-.632 0l-5 1.667a1 1 0 0 0-.684.949v6.559a1 1 0 0 0 .684.949l5 1.667a1 1 0 0 0 .632 0l5-1.667a1 1 0 0 0 .684-.949z"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M12.766 13.615 7 15.537l-5.766-1.922M7 15.5v8"></path></svg></i>
        Design system home
    </a>

    <div x-on:click="show_pages =! show_pages" class="px-2.5 py-1 my-2 font-medium bg-gray-200 rounded -mx-2 items-center cursor-pointer">
        <i class="mr-1 fa-light fa-files"></i>
        Pages
        <i class="float-right mt-1 fa-light" :class="show_pages ? 'fa-chevron-down' : 'fa-chevron-left'"></i>
    </div>

    <ul x-show="show_pages" class="mb-2" x-cloak>
        @php
            if($pagesNavigationTree !== null){
                $nav_entries = $pagesNavigationTree;
            }else{
                // TODO: include unpublished pages
                $nav_entries = Statamic::tag('nav:'.config('designsystem.navigation'))->tree();
            }
        @endphp
        @foreach ($nav_entries as $page_item)
            @php
                $s = explode('/', $page_item['url']);
                $s = end($s);
            @endphp
            <li>
                <div wire:click="loadPage('{{ $page_item['url'] }}')" class="flex justify-between font-medium cursor-pointer hover:!text-black {{ $s !== '' & $s == $slug || $s == '' & $slug == 'home' & $page_item['title'] == 'Home' ? 'active' : '' }}">
                    <div class="flex items-center w-full">
                        {{ Str::limit($page_item['title'], 25, '...') }}
                        {{-- ({{ $s.' - '.$slug }}) --}}
                        @if($pagesNavigationTree !== null && $page_item['published'] || $pagesNavigationTree === null && new \Statamic\Fields\Value(true, "published"))
                        <div class="w-2 h-2 ml-auto bg-green-500 rounded-full"></div>
                        @else
                        <div class="w-2 h-2 ml-auto bg-gray-300 rounded-full"></div>
                        @endif
                    </div>
                </div>
                @if ($page_item['children'])
                    <div class="mt-1 mb-2">
                        @foreach ($page_item['children'] as $child)
                            @php
                                $s = explode('/', $child['url']);
                                $s = end($s);
                            @endphp
                            <div wire:click="loadPage('{{ $child['url'] }}')" class="w-full flex items-center pl-2 mb-0.5 cursor-pointer hover:!text-black {{ $s == $slug ? 'active' : '' }}">
                                <i class="fa-regular fa-hyphen mr-1.5"></i>
                                {{ Str::limit($child['title'], 19, '...') }}
                                @if($pagesNavigationTree !== null && $child['published'] || $pagesNavigationTree === null && new \Statamic\Fields\Value(true, "published"))
                                <div class="w-2 h-2 ml-auto bg-green-500 rounded-full"></div>
                                @else
                                <div class="w-2 h-2 ml-auto bg-gray-300 rounded-full"></div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @endif
            </li>
        @endforeach
    </ul>

    <div x-on:click="show_components =! show_components" class="px-2.5 py-1 my-2 font-medium bg-gray-200 rounded -mx-2 items-center cursor-pointer">
        <i class="mr-1 fa-light fa-cube"></i>
        Components
        <i class="float-right mt-1 fa-light" :class="show_components ? 'fa-chevron-down' : 'fa-chevron-left'"></i>
    </div>

    <ul x-show="show_components" x-cloak>
        @foreach ($navComponents as $key => $component_item)
        <li class="{{ isset($component_item['name']) && $component_item['name'] == $component['name'] ? 'active' : '' }}" x-on:click="$dispatch('close-sub-menu')">
            @if(isset($component_item['name']))
            <a wire:click="loadComponent('{{ $component_item['slug'] }}', '{{ $component_item['group'] ?? '' }}')" class="cursor-pointer hover:!text-black">
                {{ $component_item['name'] }} <span class="text-xs text-gray-300">{{ $component_item['group'] }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path class="fa-secondary" opacity=".4" d="M95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 400a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"/><path class="fa-primary" d="M224 256c35.3 0 64-28.7 64-64c0-7.1-1.2-13.9-3.3-20.3c-1.8-5.5 1.6-11.9 7.4-11.7c40.8 1.7 77.5 29.6 88.6 71.1c13.7 51.2-16.7 103.9-67.9 117.6s-103.9-16.7-117.6-67.9c-1.9-6.9-2.9-13.9-3.2-20.7c-.3-5.8 6.1-9.2 11.7-7.4c6.4 2.1 13.2 3.3 20.3 3.3z"/></svg>
            </a>
            @else
        <li x-data="{ open: false }" x-init="$nextTick(() => { if ($el.querySelector('.ds-nav-sub-menu .active')) open = true; })" x-on:close-sub-menu.window="open = false"
            class="child">
            <div x-on:click="open =! open" class="flex items-center justify-between cursor-pointer">
                <div>
                    {{ $key }} <span class="inline-flex items-center rounded-md bg-white px-1.5 py-0.5 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/20">{{ count($component_item) }}</span>
                </div>
                <i class="fa-light" :class="open ? 'fa-chevron-down' : 'fa-chevron-left'"></i>
            </div>
            <ul x-show="open" x-cloak class="--p-2 my-1.5 ml-2 --bg-white --border rounded ds-nav-sub-menu">
                @foreach($component_item as $sub_component)
                <li class="{{ isset($sub_component['name']) && $sub_component['name'] == $component['name'] ? 'active' : '' }}">
                    <a wire:click="loadComponent('{{ $sub_component['slug'] }}', '{{ $sub_component['group'] ?? '' }}')" class="!justify-start items-center cursor-pointer hover:!text-black">
                        <i class="fa-regular fa-hyphen mr-1.5"></i> {{ $sub_component['name'] }}
                    </a>
                    </a>
                </li>
                @endforeach
            </ul>
        </li>
        @endif
        </li>
        @endforeach
    </ul>
    <div class="pt-10 mt-auto cursor-pointer">
        <a href="vscode://file{{ base_path('config/designsystem.php') }}">
            <i class="fa-solid fa-cog"></i>
            Settings
        </a>
    </div>
</nav>