<div x-data="{ tab: 'usage', showConfirm: false, device: 'desktop' }" id="view-component">
    
    <!-- Title -->
    <div class="flex items-center mt-4 mb-6">
        <div class="mr-2 ds-icon">
            @if(isset($component['icon']) && strpos($component['icon'], 'fa-') === 0)
                <i class="{{ $component['icon'] }} text-3xl mr-1"></i>
            @else
                {!! $component['icon'] ?? '' !!}
            @endif
        </div>
        <h2 class="flex text-2xl font-light tracking-normal text-gray-800">
            {{ $component['name'] }}
        </h2>
        <div x-on:click="tab = 'settings'" class="cursor-pointer ml-auto inline-flex items-center gap-x-1.5 rounded-md px-2 py-1 text-xs font-medium text-gray-900 ring-1 ring-inset ring-gray-200 uppercase">
            <svg class="h-1.5 w-1.5 component-status component-status--{{ $component['status'] }}" viewBox="0 0 6 6" aria-hidden="true">
                <circle cx="3" cy="3" r="3"></circle>
            </svg>
            {{ $component['status'] }}
        </div>
    </div>

    <!-- Preview -->
    <div class="ds-device">
        
        <div class="ds-device__bar">
            <div class="ds-device__bar-buttons">
                <div class="bg-red-400 ds-device__bar-button"></div>
                <div class="bg-yellow-400 ds-device__bar-button"></div>
                <div class="bg-green-400 ds-device__bar-button"></div>
            </div>
            <div class="ds-device__bar-devices">
                <div x-on:click="device = 'mobile'" class="ds-device__bar-device" :class="device == 'mobile' ? 'active' : ''">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path d="M96 32C78.3 32 64 46.3 64 64V448c0 17.7 14.3 32 32 32H288c17.7 0 32-14.3 32-32V64c0-17.7-14.3-32-32-32H96zM32 64C32 28.7 60.7 0 96 0H288c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H96c-35.3 0-64-28.7-64-64V64zM160 400h64c8.8 0 16 7.2 16 16s-7.2 16-16 16H160c-8.8 0-16-7.2-16-16s7.2-16 16-16z"/></svg>
                </div>
                <div x-on:click="device = 'tablet'" class="ds-device__bar-device" :class="device == 'tablet' ? 'active' : ''">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M64 32C46.3 32 32 46.3 32 64V448c0 17.7 14.3 32 32 32H384c17.7 0 32-14.3 32-32V64c0-17.7-14.3-32-32-32H64zM0 64C0 28.7 28.7 0 64 0H384c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zM192 400h64c8.8 0 16 7.2 16 16s-7.2 16-16 16H192c-8.8 0-16-7.2-16-16s7.2-16 16-16z"/></svg>
                </div>
                <div x-on:click="device = 'desktop'" class="ds-device__bar-device" :class="device == 'desktop' ? 'active' : ''">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path d="M512 32H64C46.3 32 32 46.3 32 64V256H544V64c0-17.7-14.3-32-32-32zm64 224v32 64c0 35.3-28.7 64-64 64H362.9l10.7 64H432c8.8 0 16 7.2 16 16s-7.2 16-16 16H360 216 144c-8.8 0-16-7.2-16-16s7.2-16 16-16h58.4l10.7-64H64c-35.3 0-64-28.7-64-64V288 256 64C0 28.7 28.7 0 64 0H512c35.3 0 64 28.7 64 64V256zM32 288v64c0 17.7 14.3 32 32 32H231.7c.2 0 .4 0 .6 0H343.7c.2 0 .4 0 .6 0H512c17.7 0 32-14.3 32-32V288H32zM234.9 480H341.1l-10.7-64H245.6l-10.7 64z"/></svg>
                </div>
            </div>
            <div>&nbsp;</div>
        </div>
        
        <div class="ds-device__content" :class="device == 'mobile' ? 'w-[414px]' : device == 'tablet' ? 'w-[768px]' : 'w-[1920px]'" :data-device="device">
            @include('components.'.str_replace('/', '.', $path).'.'.$slug.'--preview')
        </div>

    </div>

    <div class="flex flex-col">
        
        <!-- Tabs -->
        <div class="flex justify-between mt-4">
            <div class="flex space-x-3">
                <button x-on:click="tab = 'usage'" :class="{ '!bg-sky-500 font-bold text-white': tab === 'usage' }" class="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200">Usage</button>
                <button x-on:click="tab = 'blade'" :class="{ '!bg-sky-500 font-bold text-white': tab === 'blade' }" class="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200">Blade</button>
                <button x-on:click="tab = 'css'" :class="{ '!bg-sky-500 font-bold text-white': tab === 'css' }" class="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200">CSS</button>
                <button x-on:click="tab = 'js'" :class="{ '!bg-sky-500 font-bold text-white': tab === 'js' }" class="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200">JS</button>
                <button x-on:click="tab = 'fieldset'" :class="{ '!bg-sky-500 font-bold text-white': tab === 'fieldset' }" class="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200">Fieldset</button>
            </div>
            <button x-on:click="tab = 'settings'" :class="{ '!bg-sky-500 font-bold text-white': tab === 'settings' }" class="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded">
                <i class="fa-solid fa-cog"></i>
            </button>
        </div>

        <!-- Tab: Usage -->
        <div x-show="tab == 'usage'" class="relative mt-4" x-cloak>
            <a href="vscode://file{{ base_path('resources/views/components/'.$path.'/'.$slug.'--preview.blade.php') }}" class="btn-edit-code"><i class="mr-1 fa-solid fa-code"></i> Edit in VS Code</a>
            <pre class="mb-5 ds-code-block">
                <x-torchlight-code language="blade">{!! file_get_contents(resource_path('views/components/'.$path.'/'.$slug.'--preview.blade.php')) !!}</x-torchlight-code>
            </pre>
            {{-- @include('vendor.design-system.components.alert', ['message' => '<p>Component data can be supplied either manually or via a Statamic fieldset. If a fieldset is used, the component will automatically pull in the data from the fieldset.</p><p>The example above uses a static php data array which can be placed anywhere within the page\'s controller or view file. This is useful for components that are used in a single location and do not require updating by a client.</p>']) --}}
            @include('vendor.design-system.components.used-in', ['type' => 'component', 'component' => $component])
        </div>

        <!-- Tab: Blade -->
        <div x-show="tab == 'blade'" class="relative mt-4" x-cloak>
            <a href="vscode://file{{ base_path('resources/views/components/'.$path.'/'.$slug.'.blade.php') }}" class="btn-edit-code"><i class="mr-1 fa-solid fa-code"></i> Edit in VS Code</a>
            <pre class="mb-5 ds-code-block">
                <x-torchlight-code language="blade">{!! file_get_contents(resource_path('views/components/'.$path.'/'.$slug.'.blade.php')) !!}</x-torchlight-code>
            </pre>
        </div>

        <!-- Tab: CSS -->
        <div x-show="tab == 'css'" class="relative mt-4" x-cloak>
            <a href="vscode://file{{ base_path('resources/views/components/'.$path.'/_'.$slug.'.scss') }}" class="btn-edit-code"><i class="mr-1 fa-solid fa-code"></i> Edit in VS Code</a>
            <pre class="mb-5 ds-code-block">
                <x-torchlight-code language="scss">{!! file_get_contents(resource_path('views/components/'.$path.'/_'.$slug.'.scss')) !!}</x-torchlight-code>
            </pre>
        </div>

        <!-- Tab: JS -->
        <div x-show="tab == 'js'" class="relative mt-4" x-cloak>
            <a href="vscode://file{{ base_path('resources/views/components/'.$path.'/'.$slug.'.js') }}" class="absolute z-50 px-3 py-1 !text-sm text-white rounded bedit-codeary top-4 right-4 bg-white/10 hover:bg-white/20"><i class="mr-1 fa-solid fa-code"></i> Edit in VS Code</a>
            <pre class="mb-5 ds-code-block">
                <x-torchlight-code language="js">{!! file_get_contents(resource_path('views/components/'.$path.'/'.$slug.'.js')) !!}</x-torchlight-code>
            </pre>
        </div>

        <!-- Tab: Fieldset -->
        <div x-show="tab == 'fieldset'" class="relative mt-4" x-cloak>
            @if(file_exists(resource_path('fieldsets/'.$slug.'.yaml')))
            <div class="absolute z-50 flex space-x-2 top-4 right-4">
                <a href="/admin/fields/fieldsets/{{ $slug }}/edit" target="_blank" class="!relative btn-edit-code px-3 py-1 !top-0 !right-0 !text-sm btn-primary bg-white/10 hover:bg-white/20 flex !font-normal"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 23.48 23.68" style="width:16px; margin-right:5px"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M11.63 19.58H1.56A1.05 1.05 0 0 1 .5 18.52v-17A1.05 1.05 0 0 1 1.56.5h15.9a1.05 1.05 0 0 1 1.06 1.06v10.57"></path><rect width="13.78" height="5.3" x="2.62" y="2.62" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" rx=".53"></rect><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M2.62 10.57v6.36a.53.53 0 0 0 .53.53h6.72a.53.53 0 0 0 .53-.53.68.68 0 0 0 0-.14l-1.84-6.36a.53.53 0 0 0-.51-.43h-4.9a.53.53 0 0 0-.53.57Zm13.78 6.89v-6.89a.53.53 0 0 0-.53-.53h-4.06a.53.53 0 0 0-.53.53.39.39 0 0 0 0 .15l1.92 6.74h3.2m4.6-.28v3a3 3 0 0 1-3 3h-3m4-4 2-2 2 2"></path></svg>Edit in Statamic</a>
                <a href="vscode://file/Users/<USER>/Sites/solaris/resources/fieldsets/{{ $slug }}.yaml" class="!relative !top-0 !right-0 btn-edit-code"><i class="mr-1 fa-solid fa-code"></i> Edit in VS Code</a>
            </div>
            <pre class="mb-5 ds-code-block">
                <x-torchlight-code language="yaml">{!! file_get_contents(resource_path('fieldsets/'.$slug.'.yaml')) !!}</x-torchlight-code>
            </pre>
            @else
            @include('vendor.design-system.components.alert', ['type' => 'warning', 'message' => 'This component does not currently have a fieldset. You can still manually include this component within your markup, however it will not be available within any Bard fields until your create a fieldset. To create a fieldset, click <a href="/admin/fields/fieldsets/create" class="underline underline-offset-2" target="_blank">here</a>'])
            @endif
        </div>

        <!-- Tab: Settings -->
        <div x-show="tab == 'settings'" class="relative p-4 mt-4 border rounded-md component-settings" x-cloak>
            @include('design-system::partials.ds-component-config')
        </div>

    </div>

</div>