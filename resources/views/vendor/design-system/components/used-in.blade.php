@props([
    'type' => 'component',
    'component' => null,
    'page' => null
])
<div class="border used-in">
    <div class="">
        <div class="mt-1.5 flex mb-2">
            <i class="mt-0.5 mr-3 text-5xl fa-duotone !text-slate-500 fa-file-magnifying-glass"></i>
            <div class="flex flex-col">
                <p class="!mb-0 text-xl font-light tracking-normal">
                    {{ $type == 'page' ? 'Components' : 'Locations' }}
                </p>
                <p class="!text-gray-500">
                    {{ $type == 'page' ? 'This page contains the below components. Please note this list does not include components within any layout files (i.e. header/footer).' : 'This component is used in the following locations:' }}
                </p>
            </div>
        </div>

        @if($type == 'page')
        @if(count($page['components_used'])==0)
            <div class="text-gray-500">
                <i class="mr-1 fa-duotone fa-info-circle"></i>
                This page does not currently use any components.
            </div>
        @else
            <ul class="space-y-2">
                @foreach ($page['components_used'] as $c)
                    <li class="px-3 py-2 bg-white border rounded-md">
                        <a href="/admin/utilities/design-system/components/{{ $c['component_slug'] }}" class="flex items-center">
                            <i class="{{ $c['config']['icon'] ?? 'fa-duotone fa-cube' }} mr-3 text-xl"></i>
                            {{ $c['config']['name'] }}
                            @if($c['count'] > 1)
                            <span class="pl-1 text-gray-400">x {{ $c['count'] }}</span>
                            @endif
                            <div class="flex ml-auto space-x-2">
                                @if(strpos($c['location'], 'blade') !== false)
                                <svg x-tooltip.raw="Used in the blade template file" class="w-4" viewBox="-4 0 264 264" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="xMidYMid"><g><path d="M255.855641,59.619717 C255.950565,59.9710596 256,60.3333149 256,60.6972536 L256,117.265345 C256,118.743206 255.209409,120.108149 253.927418,120.843385 L206.448786,148.178786 L206.448786,202.359798 C206.448786,203.834322 205.665123,205.195421 204.386515,205.937838 L105.27893,262.990563 C105.05208,263.119455 104.804608,263.201946 104.557135,263.289593 C104.464333,263.320527 104.376687,263.377239 104.278729,263.403017 C103.585929,263.58546 102.857701,263.58546 102.164901,263.403017 C102.051476,263.372083 101.948363,263.310215 101.840093,263.26897 C101.613244,263.186479 101.376082,263.1143 101.159544,262.990563 L2.07258227,205.937838 C0.7913718,205.201819 0,203.837372 0,202.359798 L0,32.6555248 C0,32.2843161 0.0515567729,31.9234187 0.144358964,31.5728326 C0.175293028,31.454252 0.24747251,31.3459828 0.288717928,31.2274022 C0.366053087,31.0108638 0.438232569,30.7891697 0.55165747,30.5880982 C0.628992629,30.4540506 0.742417529,30.3457814 0.83521972,30.2220451 C0.953800298,30.0570635 1.06206952,29.8869261 1.20127281,29.7425672 C1.31985339,29.6239866 1.4745237,29.5363401 1.60857131,29.4332265 C1.75808595,29.3094903 1.89213356,29.1754427 2.06227091,29.0774848 L2.06742659,29.0774848 L51.6134853,0.551122364 C52.8901903,-0.183535768 54.4613221,-0.183535768 55.7380271,0.551122364 L105.284086,29.0774848 L105.294397,29.0774848 C105.459379,29.1805983 105.598582,29.3094903 105.748097,29.4280708 C105.882144,29.5311844 106.031659,29.6239866 106.15024,29.7374115 C106.294599,29.8869261 106.397712,30.0570635 106.521448,30.2220451 C106.609095,30.3457814 106.727676,30.4540506 106.799855,30.5880982 C106.918436,30.7943253 106.985459,31.0108638 107.06795,31.2274022 C107.109196,31.3459828 107.181375,31.454252 107.212309,31.5779883 C107.307234,31.9293308 107.355765,32.2915861 107.356668,32.6555248 L107.356668,138.651094 L148.643332,114.878266 L148.643332,60.6920979 C148.643332,60.3312005 148.694889,59.9651474 148.787691,59.619717 C148.823781,59.4959808 148.890804,59.3877116 148.93205,59.269131 C149.014541,59.0525925 149.08672,58.8308984 149.200145,58.629827 C149.27748,58.4957794 149.390905,58.3875102 149.478552,58.2637739 C149.602288,58.0987922 149.705401,57.9286549 149.84976,57.7842959 C149.968341,57.6657153 150.117856,57.5780688 150.251903,57.4749553 C150.406573,57.351219 150.540621,57.2171714 150.705603,57.1192136 L150.710758,57.1192136 L200.261973,28.5928511 C201.538395,27.8571345 203.110093,27.8571345 204.386515,28.5928511 L253.932573,57.1192136 C254.107866,57.2223271 254.241914,57.351219 254.396584,57.4697996 C254.525476,57.5729132 254.674991,57.6657153 254.793572,57.7791402 C254.93793,57.9286549 255.041044,58.0987922 255.16478,58.2637739 C255.257582,58.3875102 255.371007,58.4957794 255.443187,58.629827 C255.561767,58.8308984 255.628791,59.0525925 255.711282,59.269131 C255.757683,59.3877116 255.824707,59.4959808 255.855641,59.619717 Z M247.740605,114.878266 L247.740605,67.8378666 L230.402062,77.8192579 L206.448786,91.6106946 L206.448786,138.651094 L247.745761,114.878266 L247.740605,114.878266 Z M198.194546,199.97272 L198.194546,152.901386 L174.633101,166.357704 L107.351512,204.757188 L107.351512,252.27191 L198.194546,199.97272 Z M8.25939501,39.7961379 L8.25939501,199.97272 L99.0921175,252.266755 L99.0921175,204.762344 L51.6392637,177.906421 L51.6237967,177.89611 L51.603174,177.885798 C51.443348,177.792996 51.3093004,177.658949 51.1597857,177.545524 C51.0308938,177.44241 50.8813791,177.359919 50.7679542,177.246494 L50.7576429,177.231027 C50.6235953,177.102135 50.5307931,176.942309 50.4173682,176.79795 C50.3142546,176.658747 50.1905184,176.540167 50.1080276,176.395808 L50.1028719,176.380341 C50.0100697,176.22567 49.9533572,176.040066 49.8863334,175.864773 C49.8193096,175.710103 49.7316631,175.565744 49.6904177,175.400762 L49.6904177,175.395606 C49.6388609,175.19969 49.6285496,174.993463 49.6079269,174.792392 C49.5873041,174.637722 49.5460587,174.483051 49.5460587,174.328381 L49.5460587,174.31807 L49.5460587,63.5689658 L25.5979377,49.7723734 L8.25939501,39.8012935 L8.25939501,39.7961379 Z M53.6809119,8.89300821 L12.3994039,32.6555248 L53.6706006,56.4180414 L94.9469529,32.6503692 L53.6706006,8.89300821 L53.6809119,8.89300821 Z M75.1491521,157.19091 L99.0972731,143.404629 L99.0972731,39.7961379 L81.7587304,49.7775291 L57.8054537,63.5689658 L57.8054537,167.177457 L75.1491521,157.19091 Z M202.324244,36.934737 L161.047891,60.6972536 L202.324244,84.4597702 L243.59544,60.6920979 L202.324244,36.934737 Z M198.194546,91.6106946 L174.24127,77.8192579 L156.902727,67.8378666 L156.902727,114.878266 L180.850848,128.664547 L198.194546,138.651094 L198.194546,91.6106946 Z M103.216659,197.616575 L163.759778,163.052915 L194.023603,145.781396 L152.778185,122.034346 L105.289242,149.374903 L62.0073307,174.292291 L103.216659,197.616575 Z"></path></g></svg>
                                @endif
                                @if(strpos($c['location'], 'md') !== false)
                                <svg x-tooltip.raw="Used in the bard field" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" class="w-4"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width=".667"><path d="M2 1.067c6.82-3.274 6.9 4.713 12 1.14 1.113-.787 2.527.44 1.08 2.333l-8.413 9.533"></path><path d="M2 3.26c5.473-2.707 6.6 4 10.667 1.147L2.333 14.073m-2 0V1.24A.833.833 0 0 1 2 1.24v12.833M3.667 2.68v10.147m1.666-10.14v8.58M7 3.347v6.36M8.667 4.42v3.733m-1 5.92H.333v1A.667.667 0 0 0 1 15.74h6a.667.667 0 0 0 .667-.667z"></path></g></svg>
                                @endif
                            </div>
                        </a>
                    </li>
                @endforeach
            </ul>
        @endif
        @endif

        @if($type == 'component')
        @if(count($component['used_in'])==0)
            <div class="text-gray-500">
                <i class="mr-1 fa-duotone fa-info-circle"></i>
                This component is not currently used in any locations
            </div>
        @else
            <table class="min-w-full divide-y divide-gray-300">
                <thead>
                    <tr>
                        <th class="py-3.5 !pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">Page</th>
                        <th class="px-10 py-3.5 text-left text-sm font-semibold text-gray-900">Type</th>
                        <th class="px-10 py-3.5 text-left text-sm font-semibold text-gray-900">Template</th>
                        <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"></th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    @foreach ($component['used_in'] as $page)
                        @php
                            if($page['type']=="template"){
                                $link = "vscode://file".base_path('resources/views/'.$page['filename']).".blade.php";
                                $link_type = "Edit in VS Code";
                            }else{
                                $link = $page['edit_path'].'#content';
                                $link_type = "Edit in Statamic";
                                $template_link = "vscode://file".base_path('resources/views/'.$page['template']).".blade.php";
                            }
                        @endphp
                        <tr>
                            <td class="py-4 !pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-0">
                                <a href="{{ $link }}" target="_blank" class="hover:underline">{{ $page['filename'] }}</a>
                            </td>
                            <td class="px-10 py-4 text-sm text-gray-500 whitespace-nowrap">{{ ucfirst($page['type']) }}</td>
                            <td class="px-10 py-4 text-sm text-gray-500 whitespace-nowrap">
                                @isset($page['template'])
                                    <a href="{{ $template_link }}" class="hover:underline">
                                        {{ $page['template'] }}
                                    </a>
                                @else
                                    -
                                @endisset
                            </td>
                            <td class="px-3 py-4 text-sm text-gray-500 whitespace-nowrap">
                                <a href="{{ $link }}" target="_blank" class="group btn-edit-code !relative !bg-gray-500 hover:!bg-gray-600 !top-0 !right-0 !text-xs !text-gray-200 hover:!text-white">
                                    <i class="mr-1 fa-solid fa-code !text-gray-200 group-hover:!text-white" aria-hidden="true"></i> {{ $link_type }}
                                </a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endif
        @endif

    </div>
</div>