<div x-data="DesignSystem" id="design-system">

    @include('design-system::partials.ds-nav')
    
    <main>
        @include('design-system::partials.ds-'.$view)
    </main>

</div>

@push('scripts')    
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('DesignSystem', () => ({
            iframeUrl: '',
            init(){
                this.iframeUrl = document.getElementById('ds-site-frame').src;
            },
            checkIframeUrl() {
                const iframe = document.getElementById('ds-site-frame');
                if (this.iframeUrl !== iframe.contentWindow.location.href){
                    let url = iframe.contentWindow.location.href.replace('{{ env('APP_URL') }}', '');
                    console.log('Url has changed to '+url);
                    this.$wire.loadPage(url);
                }
                this.iframeUrl = iframe.contentWindow.location.href;
            },
            show_pages: {{ $type == 'page' ? 'true' : 'false' }},
            show_components: {{ $type == 'component' ? 'true' : 'false' }},
        }));
    });
</script>
@endpush