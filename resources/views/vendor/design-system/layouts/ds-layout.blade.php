<!doctype html>
<html>
<head>
    <title>:: Design system ::</title>
    @livewireStyles
    <script src="https://cdn.jsdelivr.net/npm/@ryangjchandler/alpine-tooltip@1.x.x/dist/cdn.min.js" defer></script>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/tippy.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap" rel="stylesheet">
    @if(config('designsystem.stylesheets'))
        @foreach(config('designsystem.stylesheets') as $stylesheet)
            <link rel="stylesheet" href="{{ $stylesheet }}">
        @endforeach
    @endif
</head>

<body>
                
    <livewire:design-system />
    
    @stack('scripts')

    @livewireScriptConfig
    
    @if(config('designsystem.fontawesome_kit_id'))
        <script src="https://kit.fontawesome.com/{{ config('designsystem.fontawesome_kit_id') }}.js" crossorigin="anonymous"></script>
    @else
        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/js/all.min.js" wire:integrity="sha512-u3fPA7V8qQmhBPNT5quvaXVa1mnnLSXUep5PS1qo5NRzHwG19aHmNJnj1Q8hpA/nBWZtZD4r4AX6YOt5ynLN2g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    @endif

    @if(config('designsystem.scripts'))
        @foreach(config('designsystem.scripts') as $script)
            <script src="{{ $script }}"></script>
        @endforeach
    @endif
    
    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('page-loaded', (event) => {
                var path = event[0].url == null ? '' : event[0].url;
                if(path == '' || path == '/'){
                    path = '/home';
                }
                history.pushState({}, '', '/admin/utilities/design-system/page'+path);
            });
            Livewire.on('component-loaded', (event) => {
                var path = event[0].path == null ? null : event[0].path;
                if(path == null){
                    history.pushState({}, '', '/admin/utilities/design-system');
                }else{
                    history.pushState({}, '', '/admin/utilities/design-system/component/'+path);
                }
            });
        });
    </script>

</body>
</html>
