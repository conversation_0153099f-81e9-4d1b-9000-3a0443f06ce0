/* Import Design System css */
import.meta.glob('/vendor/steveallinson/design-system/resources/views/components/**/*.scss', { eager: true });
import.meta.glob('/resources/views/components/**/*.scss', { eager: true });
import.meta.glob('/resources/views/pages/**/*.scss', { eager: true });


/* Import Alpine JS */
import { Livewire, Alpine } from '../../vendor/livewire/livewire/dist/livewire.esm';

/* Import Design System javascript and register with AlpineJS */
const pages = import.meta.glob('/resources/views/pages/**/*.js', { eager: true });
const custom_components = import.meta.glob('/resources/views/components/**/*.js', { eager: true });
Object.entries({ ...pages, ...custom_components }).forEach(([path, definition]) => {
    const itemName = path.split('/').pop().replace('.js', '').replace(/\W+(.)/g, function(match, chr){ return chr.toUpperCase(); })
    Alpine.data(itemName.charAt(0).toUpperCase() + itemName.slice(1) + (path.indexOf('/pages/')!=-1 ? 'Page' : ''), definition.default);
})

Livewire.start();